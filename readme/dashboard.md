# Dashboard Module

## Overview
The Dashboard serves as the main landing page and control center for the admin panel. It provides an overview of key metrics, quick access to applications and programs, and displays important statistics for users.

## Main Components

### Dashboard Stats
- **Location**: `src/components/dashboard/DashboardStats.tsx`
- **Purpose**: Displays statistical overview including charts and key performance indicators
- **Features**:
  - Overview knobs showing summary metrics
  - Statistical charts by source and category
  - Date-filtered data (current year by default)
  - Integration with reports service

### Tabbed Interface
The dashboard features a two-tab interface:

1. **Applications Tab** (Tab 1)
   - Shows applications list with filtering capabilities
   - Includes dashboard statistics
   - Application filters for refined searching
   - Multiple edit functionality for bulk operations

2. **Programs Tab** (Tab 2)
   - Displays programs list
   - Program-specific filtering options
   - Read/unread program tracking
   - Bulk selection and management

## Key Features

### Statistics and Analytics
- **Data Source**: Pulls from reports service with user-specific filtering
- **Time Range**: Defaults to current year (start of year to current date)
- **Metrics Displayed**:
  - Overview data by source
  - Category-based breakdowns
  - Visual charts and graphs

### Filtering System
- **Applications Filtering**: Advanced filtering options for applications
- **Programs Filtering**: Dedicated filtering for programs
- **Mobile Responsive**: Optimized filters for mobile devices
- **Query String Integration**: Filters persist in URL for bookmarking

### User Experience
- **Responsive Design**: Adapts to different screen sizes
- **Tab Switching**: Seamless switching between applications and programs
- **Real-time Updates**: Data refreshes based on filter changes
- **Breadcrumb Navigation**: Clear navigation context

## User Permissions
- **All Users**: Can view dashboard and basic statistics
- **Millennium Users**: Access to comprehensive analytics
- **Admin Users**: Full access to all dashboard features

## Navigation
- **Route**: `/dashboard`
- **Default View**: Applications tab with current year statistics
- **Sub-routes**: Inherits from applications and programs modules

## Technical Implementation

### State Management
- Uses React hooks for local state management
- Filter context for sharing filter state across components
- Query string integration for persistent filters

### Data Flow
1. User selects filters or switches tabs
2. Query parameters update
3. Relevant service calls fetch updated data
4. Components re-render with new data
5. Statistics and lists update accordingly

### Performance Considerations
- Memoized components to prevent unnecessary re-renders
- Efficient data fetching with proper loading states
- Optimized table rendering for large datasets

## Integration Points
- **Applications Module**: Direct integration for application management
- **Programs Module**: Seamless program viewing and editing
- **Reports Service**: Powers all statistical displays
- **User Session**: Personalizes content based on user permissions
