# Clients Module

## Overview
The Clients module manages client organizations that apply for and receive grants/awards. It handles client information, contacts, user assignments, and client-specific configurations for the grant management system.

## Main Features

### Client List
- **Route**: `/dashboard/clients`
- **Purpose**: Display and manage all client organizations
- **Features**:
  - Comprehensive client table with key information
  - Advanced filtering and search capabilities
  - Bulk operations and selections
  - Client creation and editing
  - Export functionality
  - Pagination and sorting

### Client Creation
- **Route**: `/dashboard/clients/create-client`
- **Purpose**: Add new client organizations to the system
- **Features**:
  - Complete client information form
  - User assignments during creation
  - Custom field configuration
  - State and county selection
  - Contract and billing information

### Client Editing
- **Route**: `/dashboard/clients/edit/:clientId`
- **Purpose**: Modify existing client information
- **Features**:
  - Full client details editing
  - User management and assignments
  - Client contacts management
  - Awards configuration
  - Custom fields management

## Client Information Management

### Basic Information
- **Client Name**: Organization name
- **Client Type**: Organization type classification
- **Population**: Service population size
- **State/Counties**: Geographic service areas
- **Tax ID**: Federal tax identification
- **DUNS Number**: Data Universal Numbering System identifier

### Contract Information
- **Contract Date**: Start date of service agreement
- **Contract End Date**: Expiration of service agreement
- **Billing Type**: Billing arrangement type
- **Custom Fields**: Client-specific data fields

### Awards Configuration
- **Awards Enabled**: Whether client can receive awards
- **Can Create Award**: Permission to create new awards
- **Private Awards Management**: Restricted award access
- **Award Users**: Users assigned to award management

### User Assignments
- **Assigned Users**: Users who can work with client applications
- **Primary User**: Main point of contact
- **Client Director**: Leadership contact
- **Award Users**: Users with award management access

## Client Contacts Management

### Contact Information
- **Contact Details**: Name, email, phone, position
- **Multiple Contacts**: Support for multiple client contacts
- **Contact Roles**: Different contact types and responsibilities
- **Communication Preferences**: Preferred contact methods

### Contact Operations
- **Add Contacts**: Create new client contacts
- **Edit Contacts**: Modify existing contact information
- **Delete Contacts**: Remove outdated contacts
- **Contact History**: Track contact interactions

## Client Types and Classifications

### Client Types
- **Full Clients**: Complete service clients with applications
- **Flex Clients**: Flexible service arrangements
- **Prospect Clients**: Potential future clients
- **Inactive Clients**: Former or suspended clients

### Geographic Configuration
- **State Selection**: Primary and secondary states served
- **County Selection**: Specific counties within states
- **Service Area**: Geographic boundaries for services
- **Multi-state Support**: Clients serving multiple states

## User Management Integration

### User Assignments
- **Application Users**: Users who can manage client applications
- **Award Users**: Users who can manage client awards
- **Primary User**: Main client relationship manager
- **Client Director**: Senior leadership contact

### Permission Management
- **Client-specific Access**: Users see only assigned clients
- **Role-based Permissions**: Different access levels
- **Award Access Control**: Separate award management permissions

## Filtering and Search

### Advanced Filters
- **Client Type Filter**: Filter by organization type
- **State Filter**: Filter by geographic location
- **User Assignment Filter**: Filter by assigned users
- **Status Filter**: Active, inactive, prospect status
- **Contract Date Filter**: Filter by contract periods

### Search Capabilities
- **Text Search**: Search across client names and details
- **Quick Filters**: Predefined filter combinations
- **Custom Filters**: User-defined search criteria

## Client Memo Generation

### Memo Features
- **Memo To**: Primary recipients
- **Memo CC**: Copy recipients
- **Template System**: Standardized memo formats
- **Client-specific Content**: Customized memo information

## Integration Points

### Applications Module
- **Application Assignment**: Link applications to clients
- **Client-specific Applications**: Filter applications by client
- **Application Permissions**: Client-based application access

### Awards Module
- **Award Assignment**: Link awards to clients
- **Client Award Settings**: Client-specific award configurations
- **Award Permissions**: Client-based award access

### Users Module
- **User-Client Relationships**: Manage user assignments
- **Client-specific Users**: Users created by/for specific clients
- **Permission Inheritance**: Client-based permission settings

### Programs Module
- **Program Visibility**: Client-specific program access
- **Program Eligibility**: Client qualification for programs

## User Permissions

### Millennium Admin
- Full access to all clients and features
- Can create, edit, and delete clients
- Access to all client management functions

### User Admin
- Manage clients within their scope
- Create and edit assigned clients
- User management for their clients

### Client Users
- Access to their own client information
- Limited editing capabilities
- View-only access to most features

## Technical Features

### Data Validation
- **Required Fields**: Enforce mandatory information
- **Format Validation**: Ensure proper data formats
- **Duplicate Prevention**: Prevent duplicate client entries

### Export Capabilities
- **CSV Export**: Client data for analysis
- **Excel Export**: Formatted client reports
- **Filtered Exports**: Export based on current filters

### Mobile Responsiveness
- **Mobile-optimized Forms**: Touch-friendly client editing
- **Responsive Tables**: Adaptive client list display
- **Mobile Navigation**: Optimized for mobile devices

### Performance Features
- **Pagination**: Handle large client lists efficiently
- **Lazy Loading**: Load data as needed
- **Caching**: Improve response times
- **Search Optimization**: Fast search across large datasets
