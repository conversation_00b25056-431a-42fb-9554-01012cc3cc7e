# Notifications System

## Overview
The notifications system allows users to receive updates about award status changes, payment status changes, report reminders, and compliance requirements. Notifications can be delivered through the application interface and via email.

## Notification Types
- `approvalRequest`: Sent when an award requires approval
- `approved`: Sent when an award is approved
- `rejected`: Sent when an award is rejected
- `documentReminder`: Sent when documents are required for an award
- `reportReminder`: Sent when reports are due soon
- `reportDue`: Sent when reports are due
- `reportLate`: Sent when reports are overdue
- `compliance`: Sent for compliance-related issues

## Source Types
Notifications can be associated with different source types:
- `award`: Notifications related to an award
- `report`: Notifications related to a report

## Client Preferences
Clients can configure their notification preferences:
- `awardEmailNotification`: Enable/disable email notifications for awards
- `awardBatchEmails`: Configure batch email frequency (day, week, biWeekly, month, endOfMonth, year)
- `awardReminders`: Configure reminder frequency
- `grantIdentifier`: Set grant identifier prefix

## API Endpoints

### List Notifications
```
GET /api/awards/:id/notifications
```
Returns notifications for a specific award.

### Create Notification
Notifications are created automatically based on system events, but can also be created manually through the notification service:
```typescript
notificationService.create({
  sourceId: sourceId,
  sourceType: 'award',
  type: 'compliance',
  subject: 'Notification subject',
  body: 'Notification body text'
}, userIds);
```

### Mark Notification as Read
```
PUT /api/notifications/:id/read
```
Marks a notification as read.

### Delete Notification
```
DELETE /api/notifications/:id
```
Deletes a notification.

## Scheduled Notifications
The system automatically sends several types of scheduled notifications:
- Compliance reminders
- Upcoming report reminders
- Payment status reminders
- Payment reminders to project directors

These are configured in the scheduler (`src/cron/scheduler.ts`) and implemented in the schedule service (`src/services/schedule.service.ts`).