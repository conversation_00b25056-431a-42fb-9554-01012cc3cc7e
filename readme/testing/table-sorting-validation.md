# Table Sorting and Pagination Validation Test Plan

## Overview
This document outlines the test plan for validating the fixes to table sorting and pagination issues in the admin panel.

## Issues Fixed

### 1. ProgramsList.tsx
- **Issue**: Conflicting `defaultSorted` and `sort` props causing inconsistent sorting behavior
- **Fix**: Removed `defaultSorted` prop, kept only `sort` prop for remote sorting
- **Fix**: Added condition to prevent unnecessary re-renders when sort hasn't changed
- **Fix**: Ensured pagination resets to page 1 when sorting changes

### 2. ApplicationsList.tsx  
- **Issue**: Conflicting `defaultSorted` and `sort` props causing inconsistent sorting behavior
- **Fix**: Removed `defaultSorted` prop, kept only `sort` prop for remote sorting
- **Fix**: Improved onTableChange handler to check for actual sort changes
- **Fix**: Ensured pagination resets to page 1 when sorting changes

### 3. FinanceAwardsTable.tsx
- **Issue**: Conflicting `defaultSorted` and `sort` props causing inconsistent sorting behavior  
- **Fix**: Removed `defaultSorted` prop, kept only `sort` prop for remote sorting

## Test Cases

### Test Case 1: Programs Table Sorting
**Location**: `/dashboard/programs` or `/dashboard/applications` (Programs tab)

**Steps**:
1. Navigate to the Programs page
2. Ensure there are multiple pages of data (>20 programs)
3. Click on any column header to sort (e.g., "Program Name", "Funder", "Start Date", "Due Date")
4. Verify the sort arrow appears correctly
5. Navigate to page 2 or 3 of the results
6. Click on a different column header to sort
7. Verify pagination resets to page 1
8. Verify the data is sorted across the entire dataset, not just the current page

**Expected Results**:
- Sorting should work on the entire dataset
- Pagination should reset to page 1 when sorting changes
- Sort arrows should display correctly
- All columns should have consistent sorting behavior

### Test Case 2: Applications Table Sorting
**Location**: `/dashboard/applications` (Applications tab)

**Steps**:
1. Navigate to the Applications page
2. Ensure there are multiple pages of data (>20 applications)
3. Click on any column header to sort (e.g., "Assignee", "Client", "Funder", "Program Name", "Start Date")
4. Verify the sort arrow appears correctly
5. Navigate to page 2 or 3 of the results
6. Click on a different column header to sort
7. Verify pagination resets to page 1
8. Verify the data is sorted across the entire dataset, not just the current page

**Expected Results**:
- Sorting should work on the entire dataset
- Pagination should reset to page 1 when sorting changes
- Sort arrows should display correctly
- All columns should have consistent sorting behavior

### Test Case 3: Awards Tables Sorting
**Location**: `/dashboard/awards` (all tabs: Summary, Finance, Users, Projects)

**Steps**:
1. Navigate to each awards tab (Summary, Finance, Users, Projects)
2. For each tab with sortable columns:
   - Ensure there are multiple pages of data
   - Click on column headers to sort
   - Verify sort arrows appear correctly
   - Navigate to different pages
   - Change sort and verify pagination resets to page 1
   - Verify sorting works across entire dataset

**Expected Results**:
- All awards tables should have consistent sorting behavior
- No conflicts between defaultSorted and sort props
- Pagination should reset properly when sorting changes

### Test Case 4: Client Column Sorting (Critical Test)
**Location**: `/dashboard/awards` (Summary and Finance tabs)

**Steps**:
1. Navigate to Awards Summary or Finance tab
2. Ensure there are multiple pages of awards data (>20 awards)
3. **Specifically test Client column sorting**:
   - Click on "Client" column header to sort ascending
   - Verify the sort arrow appears correctly
   - Check that awards are sorted by client name across ALL pages (not just current page)
   - Navigate to page 2 and verify client names continue in alphabetical order
   - Click "Client" column again to sort descending
   - Verify pagination resets to page 1
   - Check that awards are sorted by client name in reverse order across ALL pages
   - Navigate through multiple pages to confirm sorting is applied to entire dataset

**Expected Results**:
- Client sorting should work on the **entire dataset**, not just the current page
- When sorting ascending, client names should be in alphabetical order across all pages
- When sorting descending, client names should be in reverse alphabetical order across all pages
- Pagination should reset to page 1 when client sort changes
- No client-side JavaScript sorting should occur (sorting happens server-side)

### Test Case 5: Sort State Persistence
**Steps**:
1. Sort any table by a specific column and order
2. Navigate to a different page
3. Return to the table
4. Verify the sort state is maintained (if applicable to the implementation)

### Test Case 6: Multiple Sort Changes
**Steps**:
1. Start on any table with multiple pages
2. Sort by column A (ascending)
3. Immediately sort by column B (descending)
4. Sort by column A (descending)
5. Verify each sort change resets pagination and sorts the full dataset

**Expected Results**:
- Each sort change should be processed correctly
- No race conditions or inconsistent states
- Pagination should reset to page 1 for each sort change

## Regression Testing

### Areas to Test
1. **Filter + Sort Combinations**: Ensure filtering and sorting work together correctly
2. **Search + Sort Combinations**: Ensure search and sorting work together correctly
3. **Page Navigation**: Ensure pagination works correctly after sorting
4. **Performance**: Ensure sorting doesn't cause excessive API calls or slow performance

## Browser Testing
Test the fixes across different browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## API Validation
Verify that sorting parameters are correctly sent to the backend:
1. Open browser developer tools
2. Navigate to Network tab
3. Perform sorting operations
4. Verify API requests include correct `sortBy` and `sortOrder` parameters
5. Verify API responses return properly sorted data

## Success Criteria
- ✅ Sorting works on entire dataset, not just current page
- ✅ Pagination resets to page 1 when sorting changes
- ✅ All columns have consistent sorting behavior
- ✅ No conflicts between table props
- ✅ Sort arrows display correctly
- ✅ No performance regressions
- ✅ No race conditions or inconsistent states

## Notes
- The fixes primarily address BootstrapTable configuration issues
- Remote sorting is properly configured for server-side sorting
- State management has been improved to prevent unnecessary re-renders
