# API Documentation

## Overview
This directory contains comprehensive documentation for all API endpoints and backend services in the Millennium Strategies Admin Panel. The API is built with Node.js, Express, and TypeScript, providing RESTful endpoints for all system functionality.

## API Architecture

### Base URL
- **Development**: `http://localhost:3000`
- **Production**: Configured via environment variables

### Authentication
All API endpoints (except public ones) require authentication via JWT tokens passed in the `Authorization` header.

```
Authorization: Bearer <jwt_token>
```

### Response Format
All API responses follow a consistent JSON format:

```json
{
  "data": {},
  "message": "Success message",
  "status": 200
}
```

### Error Handling
Error responses include appropriate HTTP status codes and descriptive messages:

```json
{
  "error": "Error description",
  "message": "Detailed error message",
  "status": 400
}
```

## API Modules

### Core Modules

#### [Authentication API](./authentication.md)
Handles user login, registration, password management, and session control.

**Base Route**: `/sessions`, `/users/password`
**Key Features**:
- User login and logout
- Password creation and reset
- Token validation and refresh
- User invitation system

#### [Applications API](./applications.md)
Manages grant applications throughout their lifecycle.

**Base Route**: `/applications`
**Key Features**:
- Application CRUD operations
- Bulk operations and multiple selection
- File upload and management
- Export functionality (CSV, Excel)
- Search and filtering

#### [Awards API](./awards.md)
Comprehensive award management system with multiple sub-modules.

**Base Route**: `/awards`
**Key Features**:
- Award lifecycle management
- Budget entries and financial tracking
- Payment management
- Report management
- Document management
- User role assignments
- Timeline and status tracking

#### [Programs API](./programs.md)
Manages funding programs that clients can apply for.

**Base Route**: `/programs`
**Key Features**:
- Program CRUD operations
- Program file management
- Read tracking for programs
- Search and filtering

### User and Client Management

#### [Users API](./users.md)
User account management, permissions, and access control.

**Base Route**: `/users`
**Key Features**:
- User CRUD operations
- User invitation and creation
- Password management
- User statistics and analytics
- Role-based access control

#### [Clients API](./clients.md)
Client organization management and configuration.

**Base Route**: `/clients`
**Key Features**:
- Client CRUD operations
- Client preferences management
- Memo generation and processing
- Search and filtering

### Reporting and Utilities

#### [Reports API](./reports.md)
Analytics and reporting capabilities with export functionality.

**Base Route**: `/reports`
**Key Features**:
- Data analytics and reporting
- Multiple export formats (CSV, Excel, DOCX)
- Custom report generation
- Real-time data aggregation

#### [Files API](./files.md)
File upload, storage, and retrieval system.

**Base Route**: `/file`
**Key Features**:
- File upload and storage
- File retrieval and download
- Local and remote file handling

#### [Filters API](./filters.md)
Saved filter management for search and data filtering.

**Base Route**: `/filters`
**Key Features**:
- Filter CRUD operations
- Filter search and retrieval
- User-specific filter management

### Supporting Services

#### [Email API](./email.md)
Email sending and communication services.

**Base Route**: `/email`
**Key Features**:
- Contact email sending
- Template-based email generation
- Support request handling

#### [Funders API](./funders.md)
Funding organization search and management.

**Base Route**: `/funders`
**Key Features**:
- Funder search functionality
- Funder data retrieval

#### [Strings API](./strings.md)
System configuration and string constants.

**Base Route**: `/strings`
**Key Features**:
- Application configuration data
- Status and category definitions
- System constants and enums

## Authentication & Authorization

### JWT Token System
- **Token Generation**: Tokens are generated upon successful login
- **Token Validation**: All protected endpoints validate tokens
- **Token Expiration**: Tokens have configurable expiration times
- **Token Refresh**: Automatic token refresh for active sessions

### Role-Based Access Control (RBAC)
The system implements a comprehensive RBAC system with the following roles:

#### User Types
- **millenniumAdmin**: Full system access
- **millenniumManager**: Broad management capabilities
- **millenniumAnalyst**: Analysis and reporting access
- **userAdmin**: Client-specific administration
- **clientAdmin**: Client organization administration
- **clientAnalyst**: Client-specific analysis

#### Permission System
Permissions are defined per resource and action:
- **Resources**: application, award, client, user, program, etc.
- **Actions**: create, read, update, delete, approve, etc.

### Public Endpoints
The following endpoints do not require authentication:
- `POST /sessions` - User login
- `POST /users/password` - Password reset request
- `PATCH /users/password` - Password reset completion
- `GET /users/invite` - Invitation verification
- `POST /users/create-invited-user` - User creation from invitation
- `POST /users` - User registration
- `GET /strings` - System configuration data

## Middleware

### Authentication Middleware
- **Location**: `src/middleware/authorization.ts`
- **Purpose**: Validates JWT tokens and sets user context
- **Usage**: Applied to all protected routes

### Authorization Middleware
- **Location**: `src/middleware/auth.ts`
- **Purpose**: Implements role-based access control
- **Usage**: Applied to specific routes requiring permission checks

### Validation Middleware
- **Location**: `src/middleware/validate.ts`
- **Purpose**: Validates request data using Joi schemas
- **Usage**: Applied to routes requiring input validation

## Data Models

### Database
- **Database**: PostgreSQL
- **ORM**: Sequelize
- **Models Location**: `src/models/`

### Key Models
- **Users/Employees**: User account information
- **Clients**: Client organization data
- **Applications**: Grant application records
- **Awards**: Award management data
- **Programs**: Funding program information
- **Files**: File metadata and storage
- **Reports**: Reporting and analytics data

## Error Handling

### HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **500**: Internal Server Error

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Required field is missing",
  "status": 400,
  "details": ["Specific validation errors"]
}
```

## Rate Limiting and Security

### Security Features
- **CORS**: Configured for cross-origin requests
- **Input Validation**: All inputs validated using Joi schemas
- **SQL Injection Protection**: Sequelize ORM provides protection
- **XSS Protection**: Input sanitization and output encoding

### Performance Features
- **Pagination**: Large datasets are paginated
- **Caching**: Frequently accessed data is cached
- **Query Optimization**: Database queries are optimized
- **File Handling**: Efficient file upload and storage

## Development and Testing

### API Testing
- **Test Framework**: Jest
- **Test Location**: `src/tests/`
- **Coverage**: Unit and integration tests for controllers and services

### Development Tools
- **TypeScript**: Type-safe development
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality control

## Deployment and Monitoring

### Environment Configuration
- **Development**: Local development environment
- **Staging**: Testing environment
- **Production**: Live production environment

### Monitoring
- **Logging**: Morgan for HTTP request logging
- **Error Tracking**: Comprehensive error logging
- **Performance Monitoring**: Response time tracking
