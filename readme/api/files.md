# Files API

## Overview
The Files API handles file upload, storage, retrieval, and management for the grant management system. It supports various file types, provides secure access control, and integrates with applications, awards, and programs.

## Base Route
`/file`

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header.

## Endpoints

### Upload File

#### POST /file/upload
Uploads a file to the system.

**Request Body:** (multipart/form-data)
- `file`: File to upload (required)
- `entityType`: Type of entity ('application', 'award', 'program', 'client')
- `entityId`: ID of the associated entity
- `description`: Optional file description
- `category`: File category ('document', 'image', 'report', 'other')
- `isPublic`: Whether file is publicly accessible (default: false)

**Response:**
```json
{
  "data": {
    "file": {
      "id": 1,
      "filename": "generated_filename.pdf",
      "originalName": "Project Proposal.pdf",
      "size": 1024000,
      "mimeType": "application/pdf",
      "entityType": "application",
      "entityId": 123,
      "description": "Project proposal document",
      "category": "document",
      "isPublic": false,
      "uploadedAt": "2024-01-25T10:00:00Z",
      "uploadedBy": {
        "id": 2,
        "name": "John Doe"
      },
      "downloadUrl": "/api/file/download/1",
      "previewUrl": "/api/file/preview/1"
    }
  },
  "message": "File uploaded successfully"
}
```

**Status Codes:**
- `201`: File uploaded successfully
- `400`: Invalid file or request data
- `413`: File too large
- `415`: Unsupported file type

### Download File

#### GET /file/download/:id
Downloads a file by its ID.

**Path Parameters:**
- `id`: File ID

**Query Parameters:**
- `inline`: Whether to display inline (default: false)
- `thumbnail`: Whether to return thumbnail version (for images)

**Response:**
- File download with appropriate content type
- Content-Disposition header for download/inline display
- Content-Length header with file size

**Status Codes:**
- `200`: File downloaded successfully
- `404`: File not found
- `403`: Access denied

### Get File Information

#### GET /file/:id
Retrieves file metadata and information.

**Path Parameters:**
- `id`: File ID

**Response:**
```json
{
  "data": {
    "file": {
      "id": 1,
      "filename": "generated_filename.pdf",
      "originalName": "Project Proposal.pdf",
      "size": 1024000,
      "mimeType": "application/pdf",
      "entityType": "application",
      "entityId": 123,
      "description": "Project proposal document",
      "category": "document",
      "isPublic": false,
      "uploadedAt": "2024-01-25T10:00:00Z",
      "uploadedBy": {
        "id": 2,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "downloadCount": 5,
      "lastDownloadedAt": "2024-01-26T14:30:00Z",
      "checksum": "sha256:abc123...",
      "virusScanStatus": "clean",
      "isArchived": false
    }
  }
}
```

### List Files

#### GET /file/list
Retrieves a list of files with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `entityType`: Filter by entity type
- `entityId`: Filter by entity ID
- `category`: Filter by file category
- `mimeType`: Filter by MIME type
- `uploadedBy`: Filter by uploader user ID
- `sortBy`: Sort field ('uploadedAt', 'size', 'name')
- `sortOrder`: Sort order ('asc', 'desc')

**Response:**
```json
{
  "data": {
    "files": [
      {
        "id": 1,
        "filename": "generated_filename.pdf",
        "originalName": "Project Proposal.pdf",
        "size": 1024000,
        "mimeType": "application/pdf",
        "entityType": "application",
        "entityId": 123,
        "category": "document",
        "uploadedAt": "2024-01-25T10:00:00Z",
        "uploadedBy": {
          "id": 2,
          "name": "John Doe"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

### Update File

#### PUT /file/:id
Updates file metadata and properties.

**Path Parameters:**
- `id`: File ID

**Request Body:**
```json
{
  "description": "Updated file description",
  "category": "report",
  "isPublic": true,
  "isArchived": false
}
```

**Response:**
```json
{
  "data": {
    "file": {
      "id": 1,
      "description": "Updated file description",
      "category": "report",
      "isPublic": true,
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "File updated successfully"
}
```

### Delete File

#### DELETE /file/:id
Deletes a file from the system.

**Path Parameters:**
- `id`: File ID

**Query Parameters:**
- `permanent`: Whether to permanently delete (default: false)

**Response:**
```json
{
  "message": "File deleted successfully"
}
```

**Status Codes:**
- `200`: File deleted successfully
- `404`: File not found
- `403`: Access denied

### Get File Preview

#### GET /file/preview/:id
Generates or retrieves a preview of the file.

**Path Parameters:**
- `id`: File ID

**Query Parameters:**
- `size`: Preview size ('small', 'medium', 'large')
- `page`: Page number for multi-page documents

**Response:**
- Preview image or document
- Appropriate content type for preview format

### Bulk File Operations

#### POST /file/bulk
Performs bulk operations on multiple files.

**Request Body:**
```json
{
  "operation": "delete",
  "fileIds": [1, 2, 3, 4],
  "options": {
    "permanent": false
  }
}
```

**Response:**
```json
{
  "data": {
    "processed": 4,
    "successful": 3,
    "failed": 1,
    "results": [
      {
        "fileId": 1,
        "status": "success"
      },
      {
        "fileId": 2,
        "status": "success"
      },
      {
        "fileId": 3,
        "status": "success"
      },
      {
        "fileId": 4,
        "status": "failed",
        "error": "Access denied"
      }
    ]
  },
  "message": "Bulk operation completed"
}
```

### Download Multiple Files

#### POST /file/download/bulk
Creates a ZIP archive of multiple files for download.

**Request Body:**
```json
{
  "fileIds": [1, 2, 3],
  "archiveName": "project_documents.zip"
}
```

**Response:**
```json
{
  "data": {
    "downloadUrl": "/api/file/download/archive/temp_123",
    "archiveName": "project_documents.zip",
    "expiresAt": "2024-01-25T12:00:00Z"
  },
  "message": "Archive created successfully"
}
```

## File Storage and Management

### Storage Configuration
- **Local Storage**: Files stored on local filesystem
- **Cloud Storage**: Integration with cloud storage providers
- **Hybrid Storage**: Combination of local and cloud storage
- **Backup Storage**: Automated backup and redundancy

### File Organization
- **Directory Structure**: Organized by entity type and date
- **Naming Convention**: Unique filename generation
- **Version Control**: File version tracking
- **Archive Management**: Automated archiving of old files

### File Types and Validation

#### Supported File Types
- **Documents**: PDF, DOC, DOCX, TXT, RTF
- **Spreadsheets**: XLS, XLSX, CSV
- **Images**: JPG, JPEG, PNG, GIF, BMP
- **Archives**: ZIP, RAR, 7Z
- **Other**: Custom file types as configured

#### File Validation
- **Size Limits**: Configurable maximum file sizes
- **Type Validation**: MIME type verification
- **Content Scanning**: Virus and malware scanning
- **Format Validation**: File format integrity checks

### Security Features

#### Access Control
- **Role-based Access**: File access based on user roles
- **Entity-based Access**: Access based on entity relationships
- **Public/Private Files**: Configurable file visibility
- **Download Permissions**: Granular download permissions

#### Security Scanning
- **Virus Scanning**: Automated virus detection
- **Malware Detection**: Advanced threat detection
- **Content Analysis**: Suspicious content identification
- **Quarantine System**: Isolation of suspicious files

#### Encryption
- **At-rest Encryption**: File encryption in storage
- **In-transit Encryption**: Secure file transfers
- **Key Management**: Encryption key rotation
- **Secure Deletion**: Secure file removal

## File Processing and Conversion

### Document Processing
- **Text Extraction**: Extract text from documents
- **Metadata Extraction**: Extract document metadata
- **Thumbnail Generation**: Generate document previews
- **Format Conversion**: Convert between formats

### Image Processing
- **Thumbnail Generation**: Multiple thumbnail sizes
- **Image Optimization**: Compress and optimize images
- **Format Conversion**: Convert between image formats
- **EXIF Data Extraction**: Extract image metadata

### Archive Handling
- **Archive Extraction**: Extract archive contents
- **Archive Creation**: Create ZIP archives
- **Nested Archive Support**: Handle nested archives
- **Archive Validation**: Validate archive integrity

## Performance and Optimization

### Upload Optimization
- **Chunked Upload**: Large file upload in chunks
- **Resume Upload**: Resume interrupted uploads
- **Parallel Upload**: Multiple file upload
- **Progress Tracking**: Real-time upload progress

### Download Optimization
- **Range Requests**: Partial file downloads
- **Compression**: On-the-fly compression
- **Caching**: File caching strategies
- **CDN Integration**: Content delivery network support

### Storage Optimization
- **Deduplication**: Eliminate duplicate files
- **Compression**: File compression for storage
- **Tiered Storage**: Hot/cold storage tiers
- **Cleanup Jobs**: Automated cleanup of temporary files

## Integration Points

### Applications Module
- Application document attachments
- Application file requirements
- Application file validation
- Application file versioning

### Awards Module
- Award document management
- Award report attachments
- Award contract documents
- Award compliance files

### Programs Module
- Program guideline documents
- Program application forms
- Program resource files
- Program template documents

### Clients Module
- Client document storage
- Client contract files
- Client communication files
- Client compliance documents

## Error Handling

### Upload Errors
- **File Too Large**: Exceeds size limits
- **Invalid File Type**: Unsupported file format
- **Virus Detected**: File contains malware
- **Storage Full**: Insufficient storage space
- **Network Error**: Upload connection issues

### Download Errors
- **File Not Found**: File doesn't exist
- **Access Denied**: Insufficient permissions
- **File Corrupted**: File integrity issues
- **Server Error**: Internal server problems

### Error Response Format
```json
{
  "error": "FileUploadError",
  "message": "File size exceeds maximum limit",
  "status": 413,
  "details": {
    "maxSize": 10485760,
    "actualSize": 15728640,
    "filename": "large_document.pdf"
  }
}
```

## Monitoring and Analytics

### File Usage Analytics
- **Download Statistics**: Track file downloads
- **Popular Files**: Most accessed files
- **Storage Usage**: Storage consumption metrics
- **User Activity**: File access patterns

### Performance Monitoring
- **Upload Performance**: Upload speed and success rates
- **Download Performance**: Download speed and reliability
- **Storage Performance**: Storage system health
- **Error Rates**: File operation error tracking
