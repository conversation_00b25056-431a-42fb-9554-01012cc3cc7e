# Reports API

## Overview
The Reports API provides comprehensive analytics and reporting capabilities for the grant management system. It offers statistical insights, data visualization, and export functionality across applications, awards, clients, and programs.

## Base Route
`/reports`

## Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header.

## Endpoints

### Get Overview Data

#### GET /reports/overview
Retrieves overview statistics and key performance indicators.

**Query Parameters:**
- `startDate`: Start date for data range (ISO 8601 format)
- `endDate`: End date for data range (ISO 8601 format)
- `dateType`: Type of date to filter by ('dueDate', 'submissionDate', 'awardDate', 'createdAt')
- `clientId`: Filter by specific client ID
- `userId`: Filter by specific user ID
- `category`: Filter by category
- `source`: Filter by funding source

**Response:**
```json
{
  "data": {
    "overview": {
      "totalApplications": 150,
      "totalAwards": 45,
      "totalFunding": 2500000,
      "averageAwardAmount": 55555,
      "successRate": 0.30,
      "pendingApplications": 25,
      "activeAwards": 35,
      "completedAwards": 10,
      "totalClients": 30,
      "activeClients": 28,
      "totalPrograms": 12,
      "activePrograms": 8,
      "bySource": [
        {
          "source": "Federal",
          "applications": 75,
          "awards": 22,
          "funding": 1200000
        },
        {
          "source": "State",
          "applications": 45,
          "awards": 15,
          "funding": 800000
        },
        {
          "source": "Private",
          "applications": 30,
          "awards": 8,
          "funding": 500000
        }
      ],
      "byCategory": [
        {
          "category": "Health",
          "applications": 60,
          "awards": 18,
          "funding": 900000
        },
        {
          "category": "Education",
          "applications": 50,
          "awards": 15,
          "funding": 750000
        },
        {
          "category": "Environment",
          "applications": 40,
          "awards": 12,
          "funding": 850000
        }
      ],
      "monthlyTrends": [
        {
          "month": "2024-01",
          "applications": 12,
          "awards": 4,
          "funding": 200000
        },
        {
          "month": "2024-02",
          "applications": 15,
          "awards": 5,
          "funding": 275000
        }
      ]
    }
  }
}
```

### Get Submissions Data

#### GET /reports/submissions
Retrieves detailed submission analytics and statistics.

**Query Parameters:**
- Same filtering parameters as overview endpoint
- `groupBy`: Group results by ('month', 'quarter', 'year', 'category', 'source')
- `includeDetails`: Include detailed submission information

**Response:**
```json
{
  "data": {
    "submissions": {
      "totalSubmissions": 150,
      "submissionsByPeriod": [
        {
          "period": "2024-01",
          "submissions": 12,
          "approved": 4,
          "rejected": 3,
          "pending": 5
        }
      ],
      "submissionsByCategory": [
        {
          "category": "Health",
          "submissions": 60,
          "approvalRate": 0.30
        }
      ],
      "submissionsBySource": [
        {
          "source": "Federal",
          "submissions": 75,
          "averageProcessingTime": 45
        }
      ],
      "processingMetrics": {
        "averageProcessingTime": 42,
        "medianProcessingTime": 38,
        "fastestProcessing": 15,
        "slowestProcessing": 120
      }
    }
  }
}
```

### Get Award Amounts Data

#### GET /reports/award-amounts
Retrieves award amount analytics and distribution data.

**Query Parameters:**
- Same filtering parameters as overview endpoint
- `includeDistribution`: Include amount distribution analysis
- `includeTrends`: Include trending analysis

**Response:**
```json
{
  "data": {
    "awardAmounts": {
      "totalAwarded": 2500000,
      "averageAmount": 55555,
      "medianAmount": 50000,
      "minAmount": 10000,
      "maxAmount": 150000,
      "amountDistribution": [
        {
          "range": "0-25000",
          "count": 15,
          "percentage": 0.33
        },
        {
          "range": "25001-50000",
          "count": 18,
          "percentage": 0.40
        },
        {
          "range": "50001-100000",
          "count": 10,
          "percentage": 0.22
        },
        {
          "range": "100001+",
          "count": 2,
          "percentage": 0.05
        }
      ],
      "byCategory": [
        {
          "category": "Health",
          "totalAmount": 900000,
          "averageAmount": 50000,
          "count": 18
        }
      ],
      "bySource": [
        {
          "source": "Federal",
          "totalAmount": 1200000,
          "averageAmount": 54545,
          "count": 22
        }
      ],
      "trends": [
        {
          "period": "2024-01",
          "totalAwarded": 200000,
          "averageAmount": 50000
        }
      ]
    }
  }
}
```

### Export Report Data

#### GET /reports/export
Exports report data in various formats.

**Query Parameters:**
- `format`: Export format ('csv', 'xlsx', 'docx', 'pdf')
- `reportType`: Type of report ('overview', 'submissions', 'awards', 'detailed')
- `template`: Report template to use
- Same filtering parameters as other endpoints

**Response:**
- File download in requested format
- Content-Type varies based on format
- Content-Disposition header with filename

### Generate Custom Report

#### POST /reports/custom
Generates a custom report based on specified criteria.

**Request Body:**
```json
{
  "reportName": "Q1 2024 Performance Report",
  "reportType": "custom",
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-03-31"
  },
  "filters": {
    "clientIds": [1, 2, 3],
    "categories": ["Health", "Education"],
    "sources": ["Federal", "State"]
  },
  "sections": [
    "overview",
    "submissions",
    "awards",
    "trends"
  ],
  "format": "docx",
  "includeCharts": true,
  "includeDetails": true
}
```

**Response:**
```json
{
  "data": {
    "report": {
      "id": "report_123",
      "name": "Q1 2024 Performance Report",
      "status": "generated",
      "downloadUrl": "/api/reports/download/report_123",
      "generatedAt": "2024-01-25T10:00:00Z",
      "expiresAt": "2024-02-01T10:00:00Z"
    }
  },
  "message": "Custom report generated successfully"
}
```

### Download Generated Report

#### GET /reports/download/:reportId
Downloads a previously generated report.

**Path Parameters:**
- `reportId`: Report ID

**Response:**
- File download with appropriate content type

### Get Report Templates

#### GET /reports/templates
Retrieves available report templates.

**Response:**
```json
{
  "data": {
    "templates": [
      {
        "id": "quarterly_summary",
        "name": "Quarterly Summary Report",
        "description": "Comprehensive quarterly performance summary",
        "sections": ["overview", "trends", "highlights"],
        "formats": ["docx", "pdf"]
      },
      {
        "id": "annual_report",
        "name": "Annual Performance Report",
        "description": "Complete annual performance analysis",
        "sections": ["overview", "submissions", "awards", "trends", "analysis"],
        "formats": ["docx", "pdf", "xlsx"]
      }
    ]
  }
}
```

## Data Aggregation and Analysis

### Time-based Aggregation
- **Daily**: Day-by-day analysis
- **Weekly**: Week-over-week trends
- **Monthly**: Month-by-month comparison
- **Quarterly**: Quarterly performance
- **Yearly**: Annual trends and patterns

### Categorical Aggregation
- **By Category**: Health, Education, Environment, etc.
- **By Source**: Federal, State, Private, Foundation
- **By Client**: Client-specific performance
- **By Program**: Program effectiveness analysis
- **By User**: User performance metrics

### Statistical Analysis
- **Descriptive Statistics**: Mean, median, mode, standard deviation
- **Trend Analysis**: Growth rates, seasonal patterns
- **Comparative Analysis**: Year-over-year, period-over-period
- **Success Metrics**: Approval rates, completion rates
- **Performance Indicators**: KPIs and benchmarks

## Chart and Visualization Data

### Chart Types
- **Bar Charts**: Category comparisons
- **Line Charts**: Trend analysis over time
- **Pie Charts**: Distribution analysis
- **Scatter Plots**: Correlation analysis
- **Heat Maps**: Geographic or temporal patterns

### Chart Data Format
```json
{
  "chartData": {
    "type": "bar",
    "title": "Applications by Category",
    "labels": ["Health", "Education", "Environment"],
    "datasets": [
      {
        "label": "Applications",
        "data": [60, 50, 40],
        "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56"]
      }
    ]
  }
}
```

## Real-time Data Updates

### Data Freshness
- **Real-time**: Live data updates
- **Near Real-time**: Updates within minutes
- **Scheduled**: Regular data refresh intervals
- **On-demand**: Manual data refresh

### Caching Strategy
- **Query Caching**: Cache frequent queries
- **Result Caching**: Cache computed results
- **Incremental Updates**: Update only changed data
- **Cache Invalidation**: Smart cache refresh

## Performance Optimization

### Query Optimization
- **Indexed Queries**: Optimized database queries
- **Aggregation Pipelines**: Efficient data aggregation
- **Parallel Processing**: Concurrent data processing
- **Result Pagination**: Handle large datasets

### Data Processing
- **Background Jobs**: Async report generation
- **Streaming**: Stream large datasets
- **Compression**: Compress large reports
- **Memory Management**: Efficient memory usage

## Security and Access Control

### Data Access Control
- **Role-based Access**: Different access levels
- **Client Isolation**: Users see only authorized data
- **Field-level Security**: Sensitive data protection
- **Audit Trails**: Track data access

### Report Security
- **Secure Downloads**: Authenticated downloads
- **Temporary URLs**: Time-limited download links
- **Encryption**: Encrypt sensitive reports
- **Access Logging**: Log report access

## Error Handling

### Common Errors
- **Data Range Errors**: Invalid date ranges
- **Permission Errors**: Insufficient access rights
- **Generation Errors**: Report generation failures
- **Format Errors**: Unsupported export formats
- **Timeout Errors**: Long-running report timeouts

### Error Response Format
```json
{
  "error": "ReportGenerationError",
  "message": "Report generation failed due to timeout",
  "status": 500,
  "details": {
    "reportType": "custom",
    "timeout": 300
  }
}
```

## Integration Points

### Applications Module
- Application submission analytics
- Application status tracking
- Application performance metrics

### Awards Module
- Award distribution analysis
- Award performance tracking
- Financial analytics

### Clients Module
- Client performance metrics
- Client engagement analysis
- Client comparison reports

### Programs Module
- Program effectiveness analysis
- Program utilization metrics
- Program ROI analysis

### Users Module
- User activity analytics
- User performance metrics
- Workload distribution analysis
