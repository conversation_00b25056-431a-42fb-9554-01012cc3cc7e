# Programs API

## Overview
The Programs API manages funding programs that clients can apply for. It handles program creation, editing, file management, read tracking, and search functionality.

## Base Route
`/programs`

## Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header.

## Endpoints

### List Programs

#### GET /programs
Retrieves a list of programs with filtering and sorting.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: 'asc' or 'desc'
- `status`: Filter by program status
- `clientType`: Filter by client type visibility
- `category`: Filter by program category

**Response:**
```json
{
  "data": {
    "programs": [
      {
        "id": 1,
        "name": "Health Innovation Grant",
        "description": "Grant program for health innovation projects",
        "status": "active",
        "category": "health",
        "fundingAmount": 100000,
        "isMonetary": true,
        "applicationDeadline": "2024-06-30",
        "programStartDate": "2024-01-01",
        "programEndDate": "2024-12-31",
        "eligibilityRequirements": "Must be a registered health organization",
        "clientTypeVisibility": "full",
        "isPublic": true,
        "createdAt": "2024-01-01T10:00:00Z",
        "updatedAt": "2024-01-15T14:30:00Z",
        "files": [
          {
            "id": 1,
            "filename": "program_guidelines.pdf",
            "size": 1024000
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "pages": 2
    }
  }
}
```

### Search Programs

#### GET /programs/search
Searches programs using text query across multiple fields.

**Query Parameters:**
- `q`: Search query string
- `fields`: Comma-separated fields to search
- `limit`: Maximum results to return
- `clientType`: Filter by client type visibility

**Response:**
```json
{
  "data": {
    "programs": [
      {
        "id": 1,
        "name": "Health Innovation Grant",
        "description": "Grant program for health innovation projects",
        "category": "health",
        "fundingAmount": 100000,
        "relevanceScore": 0.95
      }
    ]
  }
}
```

### Get Program by ID

#### GET /programs/:id
Retrieves detailed information for a specific program.

**Path Parameters:**
- `id`: Program ID

**Response:**
```json
{
  "data": {
    "program": {
      "id": 1,
      "name": "Health Innovation Grant",
      "description": "Comprehensive grant program for health innovation projects",
      "status": "active",
      "category": "health",
      "subcategory": "innovation",
      "fundingAmount": 100000,
      "isMonetary": true,
      "applicationDeadline": "2024-06-30",
      "programStartDate": "2024-01-01",
      "programEndDate": "2024-12-31",
      "eligibilityRequirements": "Must be a registered health organization with 501(c)(3) status",
      "applicationRequirements": "Detailed project proposal, budget, and timeline required",
      "reportingRequirements": "Quarterly progress reports and annual financial reports",
      "clientTypeVisibility": "full",
      "isPublic": true,
      "maxApplications": 50,
      "currentApplications": 23,
      "contactEmail": "<EMAIL>",
      "contactPhone": "+**********",
      "website": "https://millennium.com/health-grants",
      "tags": ["health", "innovation", "research"],
      "customFields": {
        "priority": "high",
        "fundingSource": "federal"
      },
      "files": [
        {
          "id": 1,
          "filename": "program_guidelines.pdf",
          "originalName": "Health Innovation Grant Guidelines.pdf",
          "size": 1024000,
          "mimeType": "application/pdf",
          "uploadedAt": "2024-01-01T10:00:00Z",
          "uploadedBy": {
            "id": 1,
            "name": "Admin User"
          }
        }
      ],
      "statistics": {
        "totalApplications": 45,
        "approvedApplications": 12,
        "rejectedApplications": 8,
        "pendingApplications": 25,
        "totalFundingAwarded": 600000
      }
    }
  }
}
```

### Create Program

#### POST /programs
Creates a new program.

**Authentication Required**: Yes
**Permission**: `program:create`

**Request Body:**
```json
{
  "name": "Education Excellence Grant",
  "description": "Grant program for educational excellence initiatives",
  "status": "draft",
  "category": "education",
  "subcategory": "k12",
  "fundingAmount": 75000,
  "isMonetary": true,
  "applicationDeadline": "2024-08-31",
  "programStartDate": "2024-09-01",
  "programEndDate": "2025-08-31",
  "eligibilityRequirements": "Must be a K-12 educational institution",
  "applicationRequirements": "Educational plan and budget required",
  "reportingRequirements": "Annual progress reports",
  "clientTypeVisibility": "full",
  "isPublic": true,
  "maxApplications": 30,
  "contactEmail": "<EMAIL>",
  "tags": ["education", "k12", "excellence"],
  "customFields": {
    "priority": "medium",
    "fundingSource": "state"
  }
}
```

**Response:**
```json
{
  "data": {
    "program": {
      "id": 2,
      "name": "Education Excellence Grant",
      "status": "draft",
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Program created successfully"
}
```

### Update Program

#### PUT /programs
Updates an existing program.

**Authentication Required**: Yes
**Permission**: `program:update`

**Request Body:**
```json
{
  "id": 1,
  "name": "Updated Health Innovation Grant",
  "description": "Updated description for health innovation projects",
  "status": "active",
  "fundingAmount": 120000,
  "applicationDeadline": "2024-07-31",
  "maxApplications": 60,
  "customFields": {
    "priority": "high",
    "fundingSource": "federal",
    "newField": "new value"
  }
}
```

**Response:**
```json
{
  "data": {
    "program": {
      "id": 1,
      "name": "Updated Health Innovation Grant",
      "status": "active",
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "Program updated successfully"
}
```

### Delete Program

#### DELETE /programs
Deletes a program.

**Authentication Required**: Yes
**Permission**: `program:delete`

**Request Body:**
```json
{
  "id": 1
}
```

**Response:**
```json
{
  "message": "Program deleted successfully"
}
```

## Program Read Tracking

### Get User Program Reads

#### GET /programs/read/:userId
Retrieves programs that a specific user has read.

**Path Parameters:**
- `userId`: User ID

**Response:**
```json
{
  "data": {
    "reads": [
      {
        "id": 1,
        "userId": 2,
        "programId": 1,
        "readAt": "2024-01-20T10:00:00Z",
        "program": {
          "id": 1,
          "name": "Health Innovation Grant"
        }
      }
    ]
  }
}
```

### Create Program Reads

#### POST /programs/read
Marks programs as read by the current user.

**Request Body:**
```json
{
  "programIds": [1, 2, 3]
}
```

**Response:**
```json
{
  "data": {
    "created": 3,
    "reads": [
      {
        "id": 1,
        "userId": 2,
        "programId": 1,
        "readAt": "2024-01-25T10:00:00Z"
      },
      {
        "id": 2,
        "userId": 2,
        "programId": 2,
        "readAt": "2024-01-25T10:00:00Z"
      },
      {
        "id": 3,
        "userId": 2,
        "programId": 3,
        "readAt": "2024-01-25T10:00:00Z"
      }
    ]
  },
  "message": "Programs marked as read"
}
```

## Program File Management

### Upload Program File

#### POST /programs/programfile
Uploads a file for a program.

**Request Body:** (multipart/form-data)
- `programId`: Program ID
- `file`: File to upload
- `description`: Optional file description

**Response:**
```json
{
  "data": {
    "file": {
      "id": 2,
      "programId": 1,
      "filename": "application_form.pdf",
      "originalName": "Program Application Form.pdf",
      "size": 512000,
      "mimeType": "application/pdf",
      "uploadedAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "File uploaded successfully"
}
```

### Delete Program File

#### DELETE /programs/deletefile
Deletes a program file.

**Request Body:**
```json
{
  "fileId": 2
}
```

**Response:**
```json
{
  "message": "File deleted successfully"
}
```

## Program Status Management

### Status Types
- `draft`: Program is being created/edited
- `active`: Program is open for applications
- `closed`: Program is no longer accepting applications
- `suspended`: Program temporarily suspended
- `completed`: Program has ended
- `archived`: Program archived for historical purposes

### Status Transitions
- **Draft → Active**: Program approval and launch
- **Active → Closed**: Application deadline reached
- **Active → Suspended**: Temporary suspension
- **Suspended → Active**: Reactivation
- **Closed → Completed**: Program completion
- **Completed → Archived**: Historical archiving

## Client Type Visibility

### Visibility Options
- **full**: Visible to full clients only
- **flex**: Visible to flex clients only
- **all**: Visible to all client types
- **prospect**: Visible to prospect clients
- **custom**: Custom visibility rules

### Access Control
Programs can be configured with different visibility rules:
- Client type restrictions
- Geographic restrictions
- Organization size requirements
- Eligibility criteria

## Program Categories and Tags

### Category System
- **Primary Categories**: health, education, environment, technology
- **Subcategories**: innovation, research, community, infrastructure
- **Custom Categories**: Client-specific category definitions

### Tag System
- **Searchable Tags**: Improve program discoverability
- **Filter Tags**: Enable advanced filtering
- **Custom Tags**: Organization-specific tagging

## Funding Configuration

### Monetary Programs
- **Fixed Amount**: Specific funding amount
- **Range Amount**: Minimum and maximum funding
- **Variable Amount**: Amount determined during review
- **Matching Requirements**: Required matching funds

### Non-Monetary Programs
- **Services**: Professional services provision
- **Equipment**: Equipment or resource provision
- **Training**: Educational or training programs
- **Technical Assistance**: Expert consultation

## Application Management

### Application Limits
- **Maximum Applications**: Limit total applications
- **Per-Client Limits**: Limit applications per client
- **Time-based Limits**: Limit applications per time period
- **Geographic Limits**: Limit by geographic region

### Application Requirements
- **Required Documents**: Mandatory document submissions
- **Eligibility Criteria**: Automatic eligibility checking
- **Review Process**: Multi-stage review workflows
- **Approval Workflows**: Automated approval processes

## Reporting and Analytics

### Program Statistics
- **Application Metrics**: Total, approved, rejected, pending
- **Funding Metrics**: Total awarded, average award size
- **Timeline Metrics**: Application processing times
- **Success Metrics**: Program completion rates

### Performance Tracking
- **Goal Achievement**: Track program goal completion
- **Impact Measurement**: Measure program impact
- **ROI Analysis**: Return on investment calculations
- **Trend Analysis**: Historical performance trends

## Validation and Business Rules

### Program Data Validation
- **Required Fields**: Name, description, dates
- **Date Validation**: Logical date sequences
- **Amount Validation**: Positive funding amounts
- **File Validation**: Supported file types and sizes

### Business Rules
- **Deadline Enforcement**: Application deadline validation
- **Eligibility Rules**: Automatic eligibility checking
- **Funding Rules**: Funding amount constraints
- **Approval Rules**: Multi-level approval requirements

## Error Handling

### Common Errors
- **Validation Errors**: Invalid program data
- **Permission Errors**: Insufficient access rights
- **File Upload Errors**: File size or type issues
- **Business Rule Errors**: Program rule violations
- **Deadline Errors**: Past deadline submissions

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Application deadline must be in the future",
  "status": 400,
  "details": {
    "field": "applicationDeadline",
    "value": "2023-12-31"
  }
}
```

## Integration Points

### Applications Module
- Program-application relationships
- Application eligibility checking
- Program-specific application requirements

### Clients Module
- Client type visibility rules
- Client-specific program access
- Client program preferences

### Awards Module
- Program-award relationships
- Award creation from program applications
- Program funding tracking

### Notifications Module
- Program deadline notifications
- New program announcements
- Program status change notifications
