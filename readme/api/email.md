# Email API

## Overview
The Email API handles email sending and communication services for the grant management system. It provides contact email functionality, template-based email generation, and support request handling.

## Base Route
`/email`

## Authentication
Most endpoints require authentication via JW<PERSON> token in the Authorization header.

## Endpoints

### Send Contact Email

#### POST /email/contact
Sends a contact email from the system.

**Authentication Required**: Yes
**Permission**: `email:send`

**Request Body:**
```json
{
  "to": ["<EMAIL>"],
  "cc": ["<EMAIL>"],
  "bcc": ["<EMAIL>"],
  "subject": "Contact Request from Grant System",
  "message": "This is a contact message from the grant management system.",
  "senderName": "<PERSON>",
  "senderEmail": "<EMAIL>",
  "priority": "normal",
  "requestResponse": true
}
```

**Response:**
```json
{
  "data": {
    "emailId": "email_123",
    "status": "sent",
    "sentAt": "2024-01-25T10:00:00Z",
    "recipients": {
      "to": ["<EMAIL>"],
      "cc": ["<EMAIL>"],
      "bcc": ["<EMAIL>"]
    },
    "messageId": "msg_456"
  },
  "message": "Email sent successfully"
}
```

**Status Codes:**
- `200`: Email sent successfully
- `400`: Invalid email data
- `403`: Insufficient permissions
- `500`: Email sending failed

### Send Template Email

#### POST /email/template
Sends an email using a predefined template.

**Authentication Required**: Yes
**Permission**: `email:send`

**Request Body:**
```json
{
  "template": "award_notification",
  "to": ["<EMAIL>"],
  "data": {
    "recipientName": "Jane Smith",
    "awardName": "Community Health Grant",
    "awardAmount": 50000,
    "startDate": "2024-03-01",
    "contactPerson": "John Doe",
    "contactEmail": "<EMAIL>"
  },
  "subject": "Award Notification - Community Health Grant",
  "priority": "high"
}
```

**Response:**
```json
{
  "data": {
    "emailId": "email_124",
    "template": "award_notification",
    "status": "sent",
    "sentAt": "2024-01-25T10:00:00Z",
    "recipients": {
      "to": ["<EMAIL>"]
    }
  },
  "message": "Template email sent successfully"
}
```

### Send Bulk Emails

#### POST /email/bulk
Sends emails to multiple recipients with personalized content.

**Authentication Required**: Yes
**Permission**: `email:send`

**Request Body:**
```json
{
  "template": "payment_reminder",
  "emails": [
    {
      "to": "<EMAIL>",
      "data": {
        "clientName": "Health Organization",
        "paymentAmount": 12500,
        "dueDate": "2024-02-15",
        "awardName": "Health Innovation Grant"
      }
    },
    {
      "to": "<EMAIL>",
      "data": {
        "clientName": "Education Foundation",
        "paymentAmount": 8000,
        "dueDate": "2024-02-20",
        "awardName": "Education Excellence Grant"
      }
    }
  ],
  "subject": "Payment Reminder - {{awardName}}",
  "priority": "normal"
}
```

**Response:**
```json
{
  "data": {
    "batchId": "batch_789",
    "totalEmails": 2,
    "sent": 2,
    "failed": 0,
    "results": [
      {
        "to": "<EMAIL>",
        "status": "sent",
        "emailId": "email_125"
      },
      {
        "to": "<EMAIL>",
        "status": "sent",
        "emailId": "email_126"
      }
    ]
  },
  "message": "Bulk emails sent successfully"
}
```

### Get Email Status

#### GET /email/:id/status
Retrieves the delivery status of a sent email.

**Path Parameters:**
- `id`: Email ID

**Response:**
```json
{
  "data": {
    "emailId": "email_123",
    "status": "delivered",
    "sentAt": "2024-01-25T10:00:00Z",
    "deliveredAt": "2024-01-25T10:01:30Z",
    "openedAt": "2024-01-25T10:15:00Z",
    "clickedAt": null,
    "bounced": false,
    "bounceReason": null,
    "recipients": {
      "to": ["<EMAIL>"]
    },
    "deliveryAttempts": 1,
    "lastAttemptAt": "2024-01-25T10:00:00Z"
  }
}
```

### Get Email Templates

#### GET /email/templates
Retrieves available email templates.

**Response:**
```json
{
  "data": {
    "templates": [
      {
        "id": "award_notification",
        "name": "Award Notification",
        "description": "Notification email for award recipients",
        "category": "awards",
        "variables": [
          "recipientName",
          "awardName",
          "awardAmount",
          "startDate",
          "contactPerson"
        ],
        "subject": "Congratulations! You have received an award",
        "isActive": true
      },
      {
        "id": "payment_reminder",
        "name": "Payment Reminder",
        "description": "Reminder email for upcoming payments",
        "category": "payments",
        "variables": [
          "clientName",
          "paymentAmount",
          "dueDate",
          "awardName"
        ],
        "subject": "Payment Reminder - {{awardName}}",
        "isActive": true
      },
      {
        "id": "application_status",
        "name": "Application Status Update",
        "description": "Status update for grant applications",
        "category": "applications",
        "variables": [
          "applicantName",
          "applicationName",
          "status",
          "nextSteps"
        ],
        "subject": "Application Status Update - {{applicationName}}",
        "isActive": true
      }
    ]
  }
}
```

### Preview Email Template

#### POST /email/templates/:templateId/preview
Generates a preview of an email template with sample data.

**Path Parameters:**
- `templateId`: Template ID

**Request Body:**
```json
{
  "data": {
    "recipientName": "Jane Smith",
    "awardName": "Community Health Grant",
    "awardAmount": 50000,
    "startDate": "2024-03-01"
  }
}
```

**Response:**
```json
{
  "data": {
    "preview": {
      "subject": "Congratulations! You have received an award",
      "htmlContent": "<html><body><h1>Congratulations Jane Smith!</h1><p>You have been awarded the Community Health Grant...</p></body></html>",
      "textContent": "Congratulations Jane Smith! You have been awarded the Community Health Grant...",
      "variables": {
        "recipientName": "Jane Smith",
        "awardName": "Community Health Grant",
        "awardAmount": "$50,000",
        "startDate": "March 1, 2024"
      }
    }
  }
}
```

### Get Email History

#### GET /email/history
Retrieves email sending history for the current user or system.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `status`: Filter by email status
- `template`: Filter by template ID
- `recipient`: Filter by recipient email

**Response:**
```json
{
  "data": {
    "emails": [
      {
        "id": "email_123",
        "subject": "Award Notification - Community Health Grant",
        "template": "award_notification",
        "recipients": {
          "to": ["<EMAIL>"]
        },
        "status": "delivered",
        "sentAt": "2024-01-25T10:00:00Z",
        "deliveredAt": "2024-01-25T10:01:30Z",
        "sentBy": {
          "id": 2,
          "name": "John Doe"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

## Email Templates

### Template Structure
Email templates support dynamic content using variables and conditional logic:

```html
<!DOCTYPE html>
<html>
<head>
    <title>{{subject}}</title>
</head>
<body>
    <h1>Dear {{recipientName}},</h1>
    
    <p>Congratulations! You have been awarded the <strong>{{awardName}}</strong>.</p>
    
    {{#if awardAmount}}
    <p>Award Amount: ${{awardAmount}}</p>
    {{/if}}
    
    <p>Start Date: {{formatDate startDate}}</p>
    
    <p>If you have any questions, please contact {{contactPerson}} at {{contactEmail}}.</p>
    
    <p>Best regards,<br>
    Millennium Strategies Team</p>
</body>
</html>
```

### Template Variables
- **Dynamic Variables**: Populated from data object
- **System Variables**: Automatically populated system data
- **Helper Functions**: Date formatting, number formatting, etc.
- **Conditional Logic**: Show/hide content based on data

### Template Categories
- **Awards**: Award notifications, status updates
- **Applications**: Application confirmations, status updates
- **Payments**: Payment reminders, confirmations
- **Reports**: Report notifications, deadlines
- **System**: Welcome emails, password resets

## Email Configuration

### SMTP Settings
- **SMTP Server**: Configurable SMTP server
- **Authentication**: Username/password or OAuth
- **Encryption**: TLS/SSL support
- **Port Configuration**: Custom port settings

### Email Delivery
- **Delivery Tracking**: Track email delivery status
- **Bounce Handling**: Handle bounced emails
- **Retry Logic**: Automatic retry for failed sends
- **Rate Limiting**: Control email sending rate

### Email Validation
- **Email Format**: Validate email address format
- **Domain Validation**: Check domain existence
- **Blacklist Checking**: Check against email blacklists
- **Spam Prevention**: Anti-spam measures

## Notification Integration

### Automated Notifications
- **Award Notifications**: Automatic award notifications
- **Payment Reminders**: Scheduled payment reminders
- **Deadline Alerts**: Application and report deadlines
- **Status Updates**: Automatic status change notifications

### Batch Processing
- **Scheduled Emails**: Send emails at specific times
- **Batch Size Control**: Control batch sizes for bulk emails
- **Queue Management**: Email queue processing
- **Priority Handling**: Priority-based email sending

## Security and Privacy

### Email Security
- **Content Filtering**: Filter malicious content
- **Attachment Scanning**: Scan email attachments
- **Encryption**: Encrypt sensitive email content
- **Authentication**: Verify sender authenticity

### Privacy Protection
- **Data Anonymization**: Anonymize sensitive data
- **Consent Management**: Manage email consent
- **Unsubscribe Handling**: Handle unsubscribe requests
- **Data Retention**: Email data retention policies

## Error Handling

### Common Errors
- **Invalid Email**: Invalid email address format
- **Delivery Failure**: Email delivery failed
- **Template Error**: Template processing error
- **Permission Error**: Insufficient email permissions
- **Rate Limit**: Email rate limit exceeded

### Error Response Format
```json
{
  "error": "EmailDeliveryError",
  "message": "Failed to deliver email to recipient",
  "status": 500,
  "details": {
    "recipient": "<EMAIL>",
    "bounceReason": "Invalid recipient address",
    "retryCount": 3
  }
}
```

## Integration Points

### Awards Module
- Award notification emails
- Payment reminder emails
- Award status update emails

### Applications Module
- Application confirmation emails
- Application status update emails
- Application deadline reminders

### Users Module
- User invitation emails
- Password reset emails
- Welcome emails

### Clients Module
- Client communication emails
- Client notification emails
- Client memo emails

### Reports Module
- Report notification emails
- Report deadline reminders
- Report delivery emails
