# Applications API

## Overview
The Applications API manages grant applications throughout their lifecycle, from creation to award status. It provides comprehensive CRUD operations, bulk processing, file management, and export functionality.

## Base Route
`/applications`

## Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header.

## Endpoints

### List Applications

#### GET /applications
Retrieves a paginated list of applications with filtering and sorting.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: 'asc' or 'desc'
- `status`: Filter by application status
- `clientId`: Filter by client ID
- `assigneeId`: Filter by assignee user ID
- `programId`: Filter by program ID

**Response:**
```json
{
  "data": {
    "applications": [
      {
        "id": 1,
        "name": "Community Health Program",
        "status": "submitted",
        "fundingAmount": 50000,
        "client": {
          "id": 1,
          "name": "Health Organization"
        },
        "assignee": {
          "id": 2,
          "name": "<PERSON>"
        },
        "program": {
          "id": 1,
          "name": "Health Grants"
        },
        "createdAt": "2024-01-15T10:00:00Z",
        "updatedAt": "2024-01-20T15:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

### Search Applications

#### GET /applications/search
Searches applications using text query across multiple fields.

**Query Parameters:**
- `q`: Search query string
- `fields`: Comma-separated fields to search (name, description, etc.)
- `limit`: Maximum results to return

**Response:**
```json
{
  "data": {
    "applications": [
      {
        "id": 1,
        "name": "Community Health Program",
        "description": "Program to improve community health outcomes",
        "relevanceScore": 0.95
      }
    ]
  }
}
```

### Get Application by ID

#### GET /applications/:id
Retrieves detailed information for a specific application.

**Path Parameters:**
- `id`: Application ID

**Response:**
```json
{
  "data": {
    "application": {
      "id": 1,
      "name": "Community Health Program",
      "description": "Detailed program description",
      "status": "submitted",
      "fundingAmount": 50000,
      "varyingFundingAmount": false,
      "startDate": "2024-03-01",
      "endDate": "2025-02-28",
      "submissionDate": "2024-01-15",
      "awardDate": null,
      "notificationDate": null,
      "client": {
        "id": 1,
        "name": "Health Organization",
        "contactEmail": "<EMAIL>"
      },
      "assignee": {
        "id": 2,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "program": {
        "id": 1,
        "name": "Health Grants",
        "description": "Federal health grant program"
      },
      "funder": {
        "id": 1,
        "name": "Department of Health"
      },
      "customFields": {
        "priority": "high",
        "category": "health"
      },
      "files": [
        {
          "id": 1,
          "filename": "proposal.pdf",
          "size": 1024000,
          "uploadedAt": "2024-01-15T10:00:00Z"
        }
      ]
    }
  }
}
```

### Create Application

#### POST /applications
Creates a new application.

**Authentication Required**: Yes
**Permission**: `application:create`

**Request Body:**
```json
{
  "name": "New Grant Application",
  "description": "Application description",
  "clientId": 1,
  "programId": 1,
  "assigneeId": 2,
  "funderId": 1,
  "fundingAmount": 75000,
  "varyingFundingAmount": false,
  "startDate": "2024-04-01",
  "endDate": "2025-03-31",
  "customFields": {
    "priority": "medium",
    "category": "education"
  }
}
```

**Response:**
```json
{
  "data": {
    "application": {
      "id": 2,
      "name": "New Grant Application",
      "status": "draft",
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Application created successfully"
}
```

### Update Application

#### PUT /applications
Updates an existing application.

**Authentication Required**: Yes
**Permission**: `application:update`

**Request Body:**
```json
{
  "id": 1,
  "name": "Updated Application Name",
  "status": "awarded",
  "fundingAmount": 60000,
  "awardDate": "2024-02-01",
  "generateAward": true
}
```

**Response:**
```json
{
  "data": {
    "application": {
      "id": 1,
      "name": "Updated Application Name",
      "status": "awarded",
      "updatedAt": "2024-01-25T15:00:00Z"
    },
    "awardCreated": true
  },
  "message": "Application updated successfully"
}
```

### Delete Application

#### DELETE /applications
Deletes an application.

**Authentication Required**: Yes
**Permission**: `application:delete`

**Request Body:**
```json
{
  "id": 1
}
```

**Response:**
```json
{
  "message": "Application deleted successfully"
}
```

### Bulk Update Applications

#### PUT /applications/multiple
Updates multiple applications simultaneously.

**Authentication Required**: Yes
**Permission**: `application:update`

**Request Body:**
```json
{
  "applicationIds": [1, 2, 3],
  "updates": {
    "status": "under_review",
    "assigneeId": 5
  }
}
```

**Response:**
```json
{
  "data": {
    "updated": 3,
    "failed": 0
  },
  "message": "Applications updated successfully"
}
```

### Export Applications

#### GET /applications/export/csv
Exports applications data as CSV file.

**Query Parameters:**
- Same filtering parameters as list endpoint
- `fields`: Comma-separated list of fields to include

**Response:**
- Content-Type: `text/csv`
- File download with applications data

#### GET /applications/export/xlsx
Exports applications data as Excel file.

**Query Parameters:**
- Same filtering parameters as list endpoint
- `fields`: Comma-separated list of fields to include

**Response:**
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- File download with applications data

## Application Status Management

### Status Types
- `draft`: Initial creation state
- `submitted`: Application has been submitted
- `under_review`: Currently being evaluated
- `awarded`: Application approved for funding
- `rejected`: Application was not approved
- `withdrawn`: Application withdrawn by applicant

### Status Transitions
- **Draft → Submitted**: Application submission
- **Submitted → Under Review**: Review process begins
- **Under Review → Awarded**: Application approved
- **Under Review → Rejected**: Application denied
- **Any Status → Withdrawn**: Applicant withdrawal

### Award Creation Integration
When application status changes to "awarded":
1. System checks if client has awards enabled
2. If conditions are met, prompts for award creation
3. Creates award record in Awards module
4. Links application to award for tracking

## File Management

### File Upload
Applications support file attachments through the Files API:
- Multiple file types supported
- File size limits enforced
- Virus scanning for security
- Version control for file updates

### File Access
- Role-based file access control
- Download tracking and logging
- Bulk file operations
- File metadata management

## Custom Fields

### Dynamic Fields
Applications support client-specific custom fields:
- Text, number, date, dropdown, checkbox types
- Field validation rules
- Conditional field display
- Field history tracking

### Field Configuration
- Client-level field definitions
- Field templates and reuse
- Required vs optional fields
- Field ordering and grouping

## Search and Filtering

### Text Search
- Full-text search across application fields
- Weighted search results
- Search highlighting
- Search history and suggestions

### Advanced Filtering
- Multiple filter criteria combination
- Date range filtering
- Numeric range filtering
- Status and category filtering
- User and client filtering

### Saved Filters
- Save frequently used filter combinations
- Share filters between users
- Filter templates and presets
- Filter performance optimization

## Validation and Business Rules

### Data Validation
- Required field validation
- Format validation (dates, numbers, emails)
- Business rule validation
- Cross-field validation

### Business Rules
- Client-specific application rules
- Program eligibility requirements
- Funding amount limits
- Date consistency checks

## Performance Features

### Pagination
- Configurable page sizes
- Efficient database queries
- Total count optimization
- Cursor-based pagination for large datasets

### Caching
- Application list caching
- Search result caching
- Metadata caching
- Cache invalidation strategies

### Database Optimization
- Indexed search fields
- Query optimization
- Connection pooling
- Read replicas for reporting

## Error Handling

### Common Errors
- **Validation Errors**: Invalid input data
- **Permission Errors**: Insufficient access rights
- **Not Found Errors**: Application doesn't exist
- **Conflict Errors**: Duplicate or conflicting data

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Funding amount must be a positive number",
  "status": 400,
  "details": {
    "field": "fundingAmount",
    "value": -1000
  }
}
```
