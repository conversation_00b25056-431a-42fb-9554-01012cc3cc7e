# Clients API

## Overview
The Clients API manages client organizations that apply for and receive grants/awards. It handles client information, contacts, user assignments, preferences, and client-specific configurations.

## Base Route
`/clients`

## Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header.

## Endpoints

### List Clients

#### GET /clients
Retrieves a paginated list of clients with filtering and sorting.

**Authentication Required**: Yes
**Permission**: `client:list`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: 'asc' or 'desc'
- `clientType`: Filter by client type
- `state`: Filter by state
- `assignedUserId`: Filter by assigned user

**Response:**
```json
{
  "data": {
    "clients": [
      {
        "id": 1,
        "name": "Health Organization",
        "clientType": "full",
        "population": 50000,
        "state": "NY",
        "counties": ["New York", "Kings"],
        "taxId": "12-3456789",
        "dunsNumber": "*********",
        "contractDate": "2024-01-01",
        "contractEndDate": "2024-12-31",
        "billingType": "monthly",
        "awardsEnabled": true,
        "canCreateAward": true,
        "privateAwardsManagement": false,
        "isActive": true,
        "assignedUsers": [
          {
            "id": 2,
            "name": "John Doe",
            "role": "primary"
          }
        ],
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

### Search Clients

#### GET /clients/search
Searches clients using text query across multiple fields.

**Authentication Required**: Yes

**Query Parameters:**
- `q`: Search query string
- `fields`: Comma-separated fields to search
- `limit`: Maximum results to return

**Response:**
```json
{
  "data": {
    "clients": [
      {
        "id": 1,
        "name": "Health Organization",
        "clientType": "full",
        "state": "NY",
        "relevanceScore": 0.95
      }
    ]
  }
}
```

### Get Client by ID

#### GET /clients/:id
Retrieves detailed information for a specific client.

**Path Parameters:**
- `id`: Client ID

**Response:**
```json
{
  "data": {
    "client": {
      "id": 1,
      "name": "Health Organization",
      "clientType": "full",
      "population": 50000,
      "state": "NY",
      "counties": ["New York", "Kings"],
      "taxId": "12-3456789",
      "dunsNumber": "*********",
      "contractDate": "2024-01-01",
      "contractEndDate": "2024-12-31",
      "billingType": "monthly",
      "awardsEnabled": true,
      "canCreateAward": true,
      "privateAwardsManagement": false,
      "isActive": true,
      "contacts": [
        {
          "id": 1,
          "name": "Jane Smith",
          "email": "<EMAIL>",
          "phone": "+**********",
          "position": "Director",
          "isPrimary": true
        }
      ],
      "assignedUsers": [
        {
          "id": 2,
          "name": "John Doe",
          "email": "<EMAIL>",
          "role": "primary"
        }
      ],
      "awardUsers": [
        {
          "id": 3,
          "name": "Alice Johnson",
          "email": "<EMAIL>",
          "role": "award_manager"
        }
      ],
      "customFields": {
        "specialRequirements": "HIPAA compliance required",
        "preferredContactMethod": "email"
      },
      "preferences": {
        "emailNotifications": true,
        "batchEmailFrequency": "weekly"
      }
    }
  }
}
```

### Create Client

#### POST /clients
Creates a new client organization.

**Authentication Required**: Yes
**Permission**: `client:create`

**Request Body:**
```json
{
  "name": "New Health Organization",
  "clientType": "full",
  "population": 75000,
  "state": "CA",
  "counties": ["Los Angeles", "Orange"],
  "taxId": "98-7654321",
  "dunsNumber": "*********",
  "contractDate": "2024-02-01",
  "contractEndDate": "2025-01-31",
  "billingType": "quarterly",
  "awardsEnabled": true,
  "canCreateAward": false,
  "privateAwardsManagement": true,
  "contacts": [
    {
      "name": "Bob Wilson",
      "email": "<EMAIL>",
      "phone": "+**********",
      "position": "Executive Director",
      "isPrimary": true
    }
  ],
  "assignedUsers": [2, 3],
  "awardUsers": [4],
  "customFields": {
    "specialRequirements": "State reporting required"
  }
}
```

**Response:**
```json
{
  "data": {
    "client": {
      "id": 2,
      "name": "New Health Organization",
      "clientType": "full",
      "isActive": true,
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Client created successfully"
}
```

### Update Client

#### PUT /clients
Updates an existing client organization.

**Authentication Required**: Yes
**Permission**: `client:update`

**Request Body:**
```json
{
  "id": 1,
  "name": "Updated Health Organization",
  "population": 55000,
  "contractEndDate": "2025-12-31",
  "billingType": "annual",
  "awardsEnabled": true,
  "canCreateAward": true,
  "customFields": {
    "specialRequirements": "Updated requirements",
    "newField": "New value"
  }
}
```

**Response:**
```json
{
  "data": {
    "client": {
      "id": 1,
      "name": "Updated Health Organization",
      "population": 55000,
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "Client updated successfully"
}
```

### Delete Client

#### DELETE /clients
Deletes a client organization.

**Authentication Required**: Yes
**Permission**: `client:delete`

**Request Body:**
```json
{
  "id": 1
}
```

**Response:**
```json
{
  "message": "Client deleted successfully"
}
```

## Client Contacts Management

### List Client Contacts

#### GET /clients/:id/contacts
Retrieves contacts for a specific client.

**Path Parameters:**
- `id`: Client ID

**Response:**
```json
{
  "data": {
    "contacts": [
      {
        "id": 1,
        "clientId": 1,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "phone": "+**********",
        "position": "Director",
        "isPrimary": true,
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### Create Client Contact

#### POST /clients/:id/contacts
Creates a new contact for a client.

**Path Parameters:**
- `id`: Client ID

**Request Body:**
```json
{
  "name": "New Contact",
  "email": "<EMAIL>",
  "phone": "+1555123456",
  "position": "Program Manager",
  "isPrimary": false
}
```

### Update Client Contact

#### PUT /clients/:id/contacts/:contactId
Updates an existing client contact.

**Path Parameters:**
- `id`: Client ID
- `contactId`: Contact ID

**Request Body:**
```json
{
  "name": "Updated Contact Name",
  "position": "Senior Program Manager",
  "phone": "+1555123457"
}
```

### Delete Client Contact

#### DELETE /clients/:id/contacts/:contactId
Deletes a client contact.

**Path Parameters:**
- `id`: Client ID
- `contactId`: Contact ID

## Client Preferences Management

### Get Client Preferences

#### GET /clients/:id/preferences
Retrieves preferences for a specific client.

**Path Parameters:**
- `id`: Client ID

**Response:**
```json
{
  "data": {
    "preferences": {
      "emailNotifications": true,
      "batchEmailFrequency": "weekly",
      "grantIdentifierPrefix": "HEALTH",
      "notificationTypes": ["award_updates", "payment_reminders"],
      "communicationPreferences": {
        "preferredMethod": "email",
        "quietHours": {
          "start": "18:00",
          "end": "08:00"
        }
      }
    }
  }
}
```

### Update Client Preferences

#### PUT /clients/:id/preferences
Updates preferences for a specific client.

**Path Parameters:**
- `id`: Client ID

**Request Body:**
```json
{
  "emailNotifications": false,
  "batchEmailFrequency": "monthly",
  "grantIdentifierPrefix": "HLTH",
  "notificationTypes": ["payment_reminders"],
  "communicationPreferences": {
    "preferredMethod": "phone",
    "quietHours": {
      "start": "17:00",
      "end": "09:00"
    }
  }
}
```

## Client Memo Generation

### Generate Client Memo

#### POST /clients/:id/memo
Generates a memo document for a client.

**Path Parameters:**
- `id`: Client ID

**Request Body:**
```json
{
  "memoTo": ["<EMAIL>"],
  "memoCc": ["<EMAIL>"],
  "subject": "Quarterly Update",
  "template": "quarterly_update",
  "data": {
    "quarter": "Q1 2024",
    "applications": 5,
    "awards": 2
  }
}
```

**Response:**
```json
{
  "data": {
    "memo": {
      "id": "memo_123",
      "filename": "quarterly_update_Q1_2024.docx",
      "downloadUrl": "/api/files/download/memo_123"
    }
  },
  "message": "Memo generated successfully"
}
```

## Client Types and Classifications

### Client Types
- **full**: Full service clients with complete access
- **flex**: Flexible service arrangements
- **prospect**: Potential future clients
- **inactive**: Former or suspended clients

### Client Status Management
- **Active**: Currently receiving services
- **Inactive**: Temporarily suspended
- **Prospect**: Potential client
- **Terminated**: Service ended

## User Assignment Management

### Assign Users to Client

#### POST /clients/:id/users
Assigns users to a client with specific roles.

**Path Parameters:**
- `id`: Client ID

**Request Body:**
```json
{
  "assignments": [
    {
      "userId": 2,
      "role": "primary",
      "permissions": ["applications", "awards"]
    },
    {
      "userId": 3,
      "role": "secondary",
      "permissions": ["applications"]
    }
  ]
}
```

### Update User Assignment

#### PUT /clients/:id/users/:userId
Updates user assignment for a client.

**Path Parameters:**
- `id`: Client ID
- `userId`: User ID

**Request Body:**
```json
{
  "role": "primary",
  "permissions": ["applications", "awards", "reports"]
}
```

### Remove User Assignment

#### DELETE /clients/:id/users/:userId
Removes user assignment from a client.

**Path Parameters:**
- `id`: Client ID
- `userId`: User ID

## Awards Configuration

### Client Award Settings
- **awardsEnabled**: Whether client can receive awards
- **canCreateAward**: Permission to create new awards
- **privateAwardsManagement**: Restricted award access
- **awardUsers**: Users assigned to award management

### Award User Management
- Separate user assignments for award management
- Award-specific permissions and roles
- Award workflow configuration

## Validation and Business Rules

### Client Data Validation
- **Required Fields**: Name, client type, state
- **Format Validation**: Tax ID, DUNS number formats
- **Date Validation**: Contract date consistency
- **Email Validation**: Contact email formats

### Business Rules
- **Unique Constraints**: Client name uniqueness within scope
- **Geographic Validation**: Valid state and county combinations
- **User Assignment Rules**: Valid user-client relationships
- **Award Configuration Rules**: Consistent award settings

## Error Handling

### Common Errors
- **Validation Errors**: Invalid client data
- **Permission Errors**: Insufficient access rights
- **Not Found Errors**: Client doesn't exist
- **Conflict Errors**: Duplicate client names
- **Business Rule Errors**: Invalid configurations

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Tax ID format is invalid",
  "status": 400,
  "details": {
    "field": "taxId",
    "value": "invalid-format"
  }
}
```

## Integration Points

### Applications Module
- Client-specific application filtering
- Application assignment to clients
- Client application permissions

### Awards Module
- Client award configuration
- Award assignment to clients
- Client award permissions

### Users Module
- User-client relationship management
- Client-specific user permissions
- Client user creation and management

### Reports Module
- Client-specific reporting
- Client performance analytics
- Client data aggregation
