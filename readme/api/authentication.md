# Authentication API

## Overview
The Authentication API handles user login, registration, password management, and session control. It provides secure access to the system through JWT token-based authentication.

## Base Routes
- **Sessions**: `/sessions`
- **Password Management**: `/users/password`
- **User Invitations**: `/users/invite`
- **User Registration**: `/users`

## Endpoints

### User Login

#### POST /sessions
Creates a new user session and returns authentication token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Response:**
```json
{
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "userType": "millenniumAdmin",
    "clientCreatorId": null
  }
}
```

**Status Codes:**
- `200`: Successful login
- `400`: Invalid credentials
- `401`: Unauthorized

### User Logout

#### DELETE /sessions
Invalidates the current user session.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "message": "Successfully logged out"
}
```

**Status Codes:**
- `200`: Successful logout
- `401`: Unauthorized

### Password Management

#### POST /users/password
Initiates password reset process by sending reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "Password reset email sent"
}
```

**Status Codes:**
- `200`: Reset email sent
- `404`: User not found

#### PATCH /users/password
Completes password reset with new password.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "new_password",
  "passwordConfirmation": "new_password"
}
```

**Response:**
```json
{
  "message": "Password successfully updated"
}
```

**Status Codes:**
- `200`: Password updated
- `400`: Invalid token or password mismatch
- `404`: User not found

### User Invitation System

#### GET /users/invite
Verifies invitation token and returns invitation details.

**Query Parameters:**
- `token`: Invitation token

**Response:**
```json
{
  "valid": true,
  "invitation": {
    "email": "<EMAIL>",
    "name": "New User",
    "userType": "clientUser",
    "clientId": 123
  }
}
```

**Status Codes:**
- `200`: Valid invitation
- `400`: Invalid or expired token

#### POST /users/invite
Sends invitation emails to new users.

**Authentication Required**: Yes
**Permission**: `employee:invite`

**Request Body:**
```json
{
  "invitations": [
    {
      "name": "New User",
      "email": "<EMAIL>",
      "userType": "clientUser",
      "clientId": 123,
      "position": "Manager"
    }
  ]
}
```

**Response:**
```json
{
  "message": "Invitations sent successfully",
  "sent": 1,
  "failed": 0
}
```

**Status Codes:**
- `200`: Invitations sent
- `400`: Invalid invitation data
- `403`: Insufficient permissions

#### POST /users/create-invited-user
Creates user account from invitation token.

**Request Body:**
```json
{
  "token": "invitation_token",
  "password": "user_password",
  "passwordConfirmation": "user_password"
}
```

**Response:**
```json
{
  "message": "User account created successfully",
  "user": {
    "id": 123,
    "name": "New User",
    "email": "<EMAIL>",
    "userType": "clientUser"
  }
}
```

**Status Codes:**
- `201`: User created
- `400`: Invalid token or password mismatch

### User Registration

#### POST /users
Creates a new user account (public registration).

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "secure_password",
  "passwordConfirmation": "secure_password",
  "userType": "clientUser",
  "position": "Manager",
  "phone": "+**********"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": 124,
    "name": "John Doe",
    "email": "<EMAIL>",
    "userType": "clientUser"
  }
}
```

**Status Codes:**
- `201`: User created
- `400`: Validation errors
- `409`: Email already exists

## Authentication Flow

### Login Process
1. User submits email and password
2. System validates credentials
3. JWT token is generated and returned
4. Token is used for subsequent API requests

### Token Validation
1. Token is extracted from Authorization header
2. Token signature and expiration are verified
3. User information is loaded and attached to request
4. Request proceeds to endpoint handler

### Password Reset Flow
1. User requests password reset with email
2. Reset token is generated and emailed
3. User clicks reset link with token
4. User submits new password with token
5. Password is updated and user can login

### Invitation Flow
1. Admin sends invitation to new user
2. Invitation email with token is sent
3. User clicks invitation link
4. User creates account with invitation token
5. Account is activated and user can login

## Security Features

### JWT Token Security
- **Signing**: Tokens are signed with secret key
- **Expiration**: Configurable token expiration
- **Validation**: Comprehensive token validation
- **Refresh**: Automatic token refresh for active sessions

### Password Security
- **Hashing**: Passwords are hashed using bcrypt
- **Strength**: Password strength requirements
- **Reset Tokens**: Secure, time-limited reset tokens
- **Confirmation**: Password confirmation required

### Session Security
- **Token Storage**: Secure token storage recommendations
- **Session Timeout**: Configurable session timeouts
- **Concurrent Sessions**: Multiple session handling
- **Logout**: Secure session termination

## Error Handling

### Common Errors
- **Invalid Credentials**: Wrong email or password
- **Expired Token**: JWT token has expired
- **Invalid Token**: Malformed or invalid token
- **User Not Found**: Email not registered
- **Permission Denied**: Insufficient permissions

### Error Response Format
```json
{
  "error": "AuthenticationError",
  "message": "Invalid credentials provided",
  "status": 401
}
```

## Rate Limiting
- **Login Attempts**: Limited failed login attempts
- **Password Reset**: Limited reset requests per time period
- **Invitation Sending**: Limited invitation sending rate

## Integration Points

### User Management
- Links to user profile and preferences
- User role and permission management
- Client association and access control

### Email System
- Password reset email templates
- Invitation email templates
- Welcome email notifications

### Audit Logging
- Login/logout events
- Password change events
- Failed authentication attempts
- User creation and invitation events
