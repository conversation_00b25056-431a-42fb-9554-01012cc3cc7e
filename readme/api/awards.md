# Awards API

## Overview
The Awards API provides comprehensive award management functionality including award lifecycle management, budget tracking, payment processing, reporting, and document management. It supports multiple views and detailed award administration.

## Base Route
`/awards`

## Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header.

## Main Award Endpoints

### List Awards

#### GET /awards
Retrieves a paginated list of awards with filtering and sorting.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: 'asc' or 'desc'
- `status`: Filter by award status
- `clientId`: Filter by client ID
- `assigneeId`: Filter by assignee user ID
- `view`: View type ('summary', 'finance', 'projects', 'userView')

**Response:**
```json
{
  "data": {
    "awards": [
      {
        "id": 1,
        "name": "Community Health Award",
        "status": "active",
        "awardAmount": 50000,
        "startDate": "2024-01-01",
        "endDate": "2024-12-31",
        "client": {
          "id": 1,
          "name": "Health Organization"
        },
        "application": {
          "id": 1,
          "name": "Health Program Application"
        },
        "assignedUsers": [
          {
            "id": 2,
            "name": "<PERSON>e",
            "role": "Project Manager"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "pages": 2
    }
  }
}
```

### Get Award by ID

#### GET /awards/:id
Retrieves detailed information for a specific award.

**Path Parameters:**
- `id`: Award ID

**Response:**
```json
{
  "data": {
    "award": {
      "id": 1,
      "name": "Community Health Award",
      "description": "Award for community health improvement",
      "status": "active",
      "awardAmount": 50000,
      "startDate": "2024-01-01",
      "endDate": "2024-12-31",
      "client": {
        "id": 1,
        "name": "Health Organization"
      },
      "application": {
        "id": 1,
        "name": "Health Program Application"
      },
      "timeline": [
        {
          "id": 1,
          "status": "draft",
          "date": "2024-01-01",
          "user": "System"
        },
        {
          "id": 2,
          "status": "active",
          "date": "2024-01-15",
          "user": "John Doe"
        }
      ],
      "budgetEntries": [
        {
          "id": 1,
          "category": "Personnel",
          "amount": 30000,
          "description": "Staff salaries"
        }
      ],
      "payments": [
        {
          "id": 1,
          "amount": 12500,
          "dueDate": "2024-03-31",
          "status": "pending"
        }
      ]
    }
  }
}
```

### Create Award

#### POST /awards
Creates a new award.

**Authentication Required**: Yes
**Permission**: `award:create`

**Request Body:**
```json
{
  "name": "New Award",
  "description": "Award description",
  "clientId": 1,
  "applicationId": 1,
  "awardAmount": 75000,
  "startDate": "2024-04-01",
  "endDate": "2025-03-31",
  "status": "draft"
}
```

**Response:**
```json
{
  "data": {
    "award": {
      "id": 2,
      "name": "New Award",
      "status": "draft",
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Award created successfully"
}
```

### Update Award

#### PUT /awards
Updates an existing award.

**Authentication Required**: Yes
**Permission**: `award:update`

**Request Body:**
```json
{
  "id": 1,
  "name": "Updated Award Name",
  "status": "active",
  "awardAmount": 60000,
  "description": "Updated description"
}
```

**Response:**
```json
{
  "data": {
    "award": {
      "id": 1,
      "name": "Updated Award Name",
      "status": "active",
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "Award updated successfully"
}
```

## Budget Management

### List Budget Entries

#### GET /budgets
Retrieves budget entries for awards.

**Query Parameters:**
- `awardId`: Filter by award ID
- `version`: Budget version number

**Response:**
```json
{
  "data": {
    "budgetEntries": [
      {
        "id": 1,
        "awardId": 1,
        "category": "Personnel",
        "subcategory": "Salaries",
        "amount": 30000,
        "description": "Staff salaries for project",
        "version": 1
      }
    ]
  }
}
```

### Create Budget Entry

#### POST /budgets
Creates a new budget entry.

**Request Body:**
```json
{
  "awardId": 1,
  "category": "Equipment",
  "subcategory": "Computers",
  "amount": 5000,
  "description": "Laptop computers for staff",
  "version": 1
}
```

### Update Budget Entry

#### PUT /budgets
Updates an existing budget entry.

**Request Body:**
```json
{
  "id": 1,
  "amount": 32000,
  "description": "Updated salary amount"
}
```

### Delete Budget Entry

#### DELETE /budgets
Deletes a budget entry.

**Request Body:**
```json
{
  "id": 1
}
```

## Payment Management

### List Payments

#### GET /payments
Retrieves payment records for awards.

**Query Parameters:**
- `awardId`: Filter by award ID
- `status`: Filter by payment status

**Response:**
```json
{
  "data": {
    "payments": [
      {
        "id": 1,
        "awardId": 1,
        "amount": 12500,
        "dueDate": "2024-03-31",
        "status": "pending",
        "description": "Q1 payment",
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### Create Payment

#### POST /payments
Creates a new payment record.

**Request Body:**
```json
{
  "awardId": 1,
  "amount": 12500,
  "dueDate": "2024-06-30",
  "description": "Q2 payment",
  "status": "pending"
}
```

### Update Payment

#### PUT /payments
Updates an existing payment record.

**Request Body:**
```json
{
  "id": 1,
  "status": "completed",
  "paidDate": "2024-03-28",
  "notes": "Payment processed successfully"
}
```

## Report Management

### List Reports

#### GET /reports
Retrieves report records for awards.

**Query Parameters:**
- `awardId`: Filter by award ID
- `status`: Filter by report status
- `type`: Filter by report type

**Response:**
```json
{
  "data": {
    "reports": [
      {
        "id": 1,
        "awardId": 1,
        "name": "Quarterly Progress Report",
        "type": "progress",
        "status": "pending",
        "dueDate": "2024-04-15",
        "submittedDate": null
      }
    ]
  }
}
```

### Create Report

#### POST /reports
Creates a new report requirement.

**Request Body:**
```json
{
  "awardId": 1,
  "name": "Annual Financial Report",
  "type": "financial",
  "dueDate": "2024-12-31",
  "description": "Annual financial reporting requirement"
}
```

### Update Report

#### PUT /reports
Updates an existing report.

**Request Body:**
```json
{
  "id": 1,
  "status": "submitted",
  "submittedDate": "2024-04-10",
  "notes": "Report submitted on time"
}
```

## Document Management

### List Documents

#### GET /documents
Retrieves documents associated with awards.

**Query Parameters:**
- `awardId`: Filter by award ID
- `type`: Filter by document type

**Response:**
```json
{
  "data": {
    "documents": [
      {
        "id": 1,
        "awardId": 1,
        "filename": "award_agreement.pdf",
        "type": "agreement",
        "size": 1024000,
        "uploadedAt": "2024-01-15T10:00:00Z",
        "uploadedBy": {
          "id": 2,
          "name": "John Doe"
        }
      }
    ]
  }
}
```

### Upload Document

#### POST /documents
Uploads a new document for an award.

**Request Body:** (multipart/form-data)
- `awardId`: Award ID
- `type`: Document type
- `file`: File to upload

**Response:**
```json
{
  "data": {
    "document": {
      "id": 2,
      "filename": "progress_report.pdf",
      "type": "report",
      "size": 512000
    }
  },
  "message": "Document uploaded successfully"
}
```

### Download Document

#### GET /documents/:id/download
Downloads a specific document.

**Path Parameters:**
- `id`: Document ID

**Response:**
- File download with appropriate content type

## User Role Management

### List Award Users

#### GET /users
Retrieves users assigned to awards.

**Query Parameters:**
- `awardId`: Filter by award ID

**Response:**
```json
{
  "data": {
    "users": [
      {
        "id": 1,
        "awardId": 1,
        "userId": 2,
        "role": "Project Manager",
        "permissions": ["read", "write", "approve"],
        "user": {
          "id": 2,
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ]
  }
}
```

### Assign User to Award

#### POST /users
Assigns a user to an award with specific role.

**Request Body:**
```json
{
  "awardId": 1,
  "userId": 2,
  "role": "Financial Manager",
  "permissions": ["read", "write"]
}
```

### Update User Role

#### PUT /users
Updates user role and permissions for an award.

**Request Body:**
```json
{
  "id": 1,
  "role": "Senior Project Manager",
  "permissions": ["read", "write", "approve", "delete"]
}
```

### Remove User from Award

#### DELETE /users
Removes user assignment from an award.

**Request Body:**
```json
{
  "id": 1
}
```

## Timeline Management

### Get Award Timeline

#### GET /timeline
Retrieves timeline events for an award.

**Query Parameters:**
- `awardId`: Award ID

**Response:**
```json
{
  "data": {
    "timeline": [
      {
        "id": 1,
        "awardId": 1,
        "event": "Award Created",
        "status": "draft",
        "date": "2024-01-01T10:00:00Z",
        "userId": 1,
        "user": {
          "name": "System"
        },
        "notes": "Award created from application"
      }
    ]
  }
}
```

### Add Timeline Event

#### POST /timeline
Adds a new timeline event.

**Request Body:**
```json
{
  "awardId": 1,
  "event": "Status Changed",
  "status": "active",
  "notes": "Award activated after approval"
}
```

## Award Status Management

### Status Types
- `draft`: Initial creation state
- `active`: Award is active and in progress
- `suspended`: Award temporarily suspended
- `completed`: Award successfully completed
- `terminated`: Award terminated early
- `closed`: Award closed and archived

### Status Transitions
- **Draft → Active**: Award approval and activation
- **Active → Suspended**: Temporary suspension
- **Suspended → Active**: Reactivation
- **Active → Completed**: Successful completion
- **Active → Terminated**: Early termination
- **Completed/Terminated → Closed**: Final closure

## Export and Reporting

### Export Awards Data

#### GET /awards/export
Exports awards data in various formats.

**Query Parameters:**
- `format`: Export format ('csv', 'xlsx', 'pdf')
- `view`: Data view ('summary', 'finance', 'detailed')
- Filtering parameters from list endpoint

**Response:**
- File download in requested format

## Error Handling

### Common Errors
- **Validation Errors**: Invalid input data
- **Permission Errors**: Insufficient access rights
- **Not Found Errors**: Award doesn't exist
- **Business Rule Errors**: Award business logic violations

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Award amount must be positive",
  "status": 400,
  "details": {
    "field": "awardAmount",
    "value": -1000
  }
}
```
