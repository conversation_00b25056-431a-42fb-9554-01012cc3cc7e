# Strings API

## Overview
The Strings API provides system configuration data, constants, and string definitions used throughout the grant management system. It serves as a centralized source for application configuration, status definitions, categories, and other system constants.

## Base Route
`/strings`

## Authentication
This endpoint is public and does not require authentication.

## Endpoints

### Get System Strings

#### GET /strings
Retrieves all system configuration strings and constants.

**Response:**
```json
{
  "data": {
    "applicationStatuses": [
      {
        "id": 1,
        "code": "draft",
        "name": "Draft",
        "description": "Application is being created or edited",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "submitted",
        "name": "Submitted",
        "description": "Application has been submitted for review",
        "color": "#007bff",
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "under_review",
        "name": "Under Review",
        "description": "Application is currently being reviewed",
        "color": "#ffc107",
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "awarded",
        "name": "Awarded",
        "description": "Application has been approved for funding",
        "color": "#28a745",
        "isActive": true,
        "sortOrder": 4
      },
      {
        "id": 5,
        "code": "rejected",
        "name": "Rejected",
        "description": "Application was not approved",
        "color": "#dc3545",
        "isActive": true,
        "sortOrder": 5
      },
      {
        "id": 6,
        "code": "withdrawn",
        "name": "Withdrawn",
        "description": "Application was withdrawn by applicant",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 6
      }
    ],
    "awardStatuses": [
      {
        "id": 1,
        "code": "draft",
        "name": "Draft",
        "description": "Award is being created or configured",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "active",
        "name": "Active",
        "description": "Award is active and in progress",
        "color": "#28a745",
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "suspended",
        "name": "Suspended",
        "description": "Award has been temporarily suspended",
        "color": "#ffc107",
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "completed",
        "name": "Completed",
        "description": "Award has been successfully completed",
        "color": "#17a2b8",
        "isActive": true,
        "sortOrder": 4
      },
      {
        "id": 5,
        "code": "terminated",
        "name": "Terminated",
        "description": "Award was terminated early",
        "color": "#dc3545",
        "isActive": true,
        "sortOrder": 5
      },
      {
        "id": 6,
        "code": "closed",
        "name": "Closed",
        "description": "Award is closed and archived",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 6
      }
    ],
    "clientTypes": [
      {
        "id": 1,
        "code": "full",
        "name": "Full Client",
        "description": "Complete service client with full access",
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "flex",
        "name": "Flex Client",
        "description": "Flexible service arrangement client",
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "prospect",
        "name": "Prospect",
        "description": "Potential future client",
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "inactive",
        "name": "Inactive",
        "description": "Former or suspended client",
        "isActive": false,
        "sortOrder": 4
      }
    ],
    "userTypes": [
      {
        "id": 1,
        "code": "millenniumAdmin",
        "name": "Millennium Admin",
        "description": "Full system administrator",
        "permissions": ["all"],
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "millenniumManager",
        "name": "Millennium Manager",
        "description": "System manager with broad access",
        "permissions": ["manage_users", "manage_clients", "manage_applications", "manage_awards"],
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "millenniumAnalyst",
        "name": "Millennium Analyst",
        "description": "System analyst with reporting access",
        "permissions": ["view_reports", "export_data", "view_applications", "view_awards"],
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "userAdmin",
        "name": "User Admin",
        "description": "Client-specific user administrator",
        "permissions": ["manage_client_users", "view_client_data"],
        "isActive": true,
        "sortOrder": 4
      },
      {
        "id": 5,
        "code": "clientAdmin",
        "name": "Client Admin",
        "description": "Client organization administrator",
        "permissions": ["manage_applications", "view_awards", "manage_contacts"],
        "isActive": true,
        "sortOrder": 5
      },
      {
        "id": 6,
        "code": "clientUser",
        "name": "Client User",
        "description": "Standard client organization user",
        "permissions": ["view_applications", "create_applications"],
        "isActive": true,
        "sortOrder": 6
      }
    ],
    "categories": [
      {
        "id": 1,
        "code": "health",
        "name": "Health",
        "description": "Health and medical related programs",
        "subcategories": [
          {
            "id": 1,
            "code": "public_health",
            "name": "Public Health",
            "description": "Community and population health initiatives"
          },
          {
            "id": 2,
            "code": "medical_research",
            "name": "Medical Research",
            "description": "Medical and health research projects"
          },
          {
            "id": 3,
            "code": "mental_health",
            "name": "Mental Health",
            "description": "Mental health and behavioral health programs"
          }
        ],
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "education",
        "name": "Education",
        "description": "Educational programs and initiatives",
        "subcategories": [
          {
            "id": 4,
            "code": "k12",
            "name": "K-12 Education",
            "description": "Primary and secondary education programs"
          },
          {
            "id": 5,
            "code": "higher_education",
            "name": "Higher Education",
            "description": "College and university programs"
          },
          {
            "id": 6,
            "code": "adult_education",
            "name": "Adult Education",
            "description": "Adult learning and continuing education"
          }
        ],
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "environment",
        "name": "Environment",
        "description": "Environmental and conservation programs",
        "subcategories": [
          {
            "id": 7,
            "code": "conservation",
            "name": "Conservation",
            "description": "Natural resource conservation projects"
          },
          {
            "id": 8,
            "code": "climate_change",
            "name": "Climate Change",
            "description": "Climate change mitigation and adaptation"
          }
        ],
        "isActive": true,
        "sortOrder": 3
      }
    ],
    "fundingSources": [
      {
        "id": 1,
        "code": "federal",
        "name": "Federal",
        "description": "Federal government funding",
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "state",
        "name": "State",
        "description": "State government funding",
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "local",
        "name": "Local",
        "description": "Local government funding",
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "foundation",
        "name": "Foundation",
        "description": "Private foundation funding",
        "isActive": true,
        "sortOrder": 4
      },
      {
        "id": 5,
        "code": "corporate",
        "name": "Corporate",
        "description": "Corporate funding and sponsorship",
        "isActive": true,
        "sortOrder": 5
      }
    ],
    "paymentStatuses": [
      {
        "id": 1,
        "code": "pending",
        "name": "Pending",
        "description": "Payment is scheduled but not yet processed",
        "color": "#ffc107",
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "processing",
        "name": "Processing",
        "description": "Payment is currently being processed",
        "color": "#007bff",
        "isActive": true,
        "sortOrder": 2
      },
      {
        "id": 3,
        "code": "completed",
        "name": "Completed",
        "description": "Payment has been successfully completed",
        "color": "#28a745",
        "isActive": true,
        "sortOrder": 3
      },
      {
        "id": 4,
        "code": "failed",
        "name": "Failed",
        "description": "Payment processing failed",
        "color": "#dc3545",
        "isActive": true,
        "sortOrder": 4
      },
      {
        "id": 5,
        "code": "cancelled",
        "name": "Cancelled",
        "description": "Payment was cancelled",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 5
      }
    ],
    "reportTypes": [
      {
        "id": 1,
        "code": "progress",
        "name": "Progress Report",
        "description": "Regular progress reporting",
        "frequency": "quarterly",
        "isRequired": true,
        "isActive": true
      },
      {
        "id": 2,
        "code": "financial",
        "name": "Financial Report",
        "description": "Financial expenditure reporting",
        "frequency": "annual",
        "isRequired": true,
        "isActive": true
      },
      {
        "id": 3,
        "code": "final",
        "name": "Final Report",
        "description": "Project completion report",
        "frequency": "once",
        "isRequired": true,
        "isActive": true
      },
      {
        "id": 4,
        "code": "compliance",
        "name": "Compliance Report",
        "description": "Regulatory compliance reporting",
        "frequency": "as_needed",
        "isRequired": false,
        "isActive": true
      }
    ],
    "systemSettings": {
      "applicationSettings": {
        "maxFileSize": 10485760,
        "allowedFileTypes": ["pdf", "doc", "docx", "xls", "xlsx", "txt"],
        "defaultStatus": "draft",
        "autoSaveInterval": 30000
      },
      "awardSettings": {
        "defaultCurrency": "USD",
        "maxBudgetEntries": 50,
        "requireApproval": true,
        "autoCreateFromApplication": true
      },
      "emailSettings": {
        "batchSizes": [
          {"code": "daily", "name": "Daily", "value": 1},
          {"code": "weekly", "name": "Weekly", "value": 7},
          {"code": "biweekly", "name": "Bi-weekly", "value": 14},
          {"code": "monthly", "name": "Monthly", "value": 30}
        ],
        "defaultTemplate": "standard",
        "maxRecipients": 100
      },
      "reportSettings": {
        "exportFormats": ["csv", "xlsx", "pdf", "docx"],
        "maxExportRows": 10000,
        "defaultDateRange": 365
      }
    },
    "validationRules": {
      "email": {
        "pattern": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
        "maxLength": 255
      },
      "phone": {
        "pattern": "^\\+?[1-9]\\d{1,14}$",
        "maxLength": 20
      },
      "currency": {
        "pattern": "^\\d+(\\.\\d{1,2})?$",
        "maxValue": 999999999.99
      },
      "percentage": {
        "pattern": "^(100|[1-9]?\\d)(\\.\\d{1,2})?$",
        "minValue": 0,
        "maxValue": 100
      }
    }
  }
}
```

### Get Specific String Category

#### GET /strings/:category
Retrieves strings for a specific category.

**Path Parameters:**
- `category`: Category name ('applicationStatuses', 'awardStatuses', 'clientTypes', etc.)

**Response:**
```json
{
  "data": {
    "category": "applicationStatuses",
    "items": [
      {
        "id": 1,
        "code": "draft",
        "name": "Draft",
        "description": "Application is being created or edited",
        "color": "#6c757d",
        "isActive": true,
        "sortOrder": 1
      }
    ]
  }
}
```

## String Categories

### Application Statuses
Defines the various states an application can be in throughout its lifecycle.

### Award Statuses
Defines the various states an award can be in from creation to completion.

### Client Types
Defines the different types of client organizations in the system.

### User Types
Defines the different user roles and their associated permissions.

### Categories and Subcategories
Defines the hierarchical category system for organizing applications and awards.

### Funding Sources
Defines the different sources of funding (federal, state, foundation, etc.).

### Payment Statuses
Defines the various states of payment processing.

### Report Types
Defines the different types of reports and their requirements.

## System Settings

### Application Settings
Configuration for application-related functionality:
- File upload limits and allowed types
- Default status values
- Auto-save intervals

### Award Settings
Configuration for award-related functionality:
- Currency settings
- Budget entry limits
- Approval requirements

### Email Settings
Configuration for email functionality:
- Batch processing options
- Template settings
- Recipient limits

### Report Settings
Configuration for reporting functionality:
- Export format options
- Row limits for exports
- Default date ranges

## Validation Rules

### Data Validation Patterns
Regular expressions and rules for validating:
- Email addresses
- Phone numbers
- Currency amounts
- Percentages
- Other data types

### Field Constraints
Maximum lengths, value ranges, and other constraints for form fields.

## Usage in Frontend

### Status Display
The strings are used throughout the frontend to display consistent status names and colors:

```javascript
// Example usage in React component
const getStatusColor = (statusCode) => {
  const status = applicationStatuses.find(s => s.code === statusCode);
  return status?.color || '#6c757d';
};
```

### Form Validation
Validation rules are used for client-side form validation:

```javascript
// Example email validation
const emailPattern = validationRules.email.pattern;
const isValidEmail = new RegExp(emailPattern).test(email);
```

### Dropdown Options
Categories and types are used to populate dropdown menus and filter options.

## Caching and Performance

### Client-side Caching
The strings data is typically cached on the client side since it changes infrequently.

### Cache Invalidation
When system configuration changes, clients should refresh their cached strings data.

### Performance Optimization
- Minimal data transfer with only necessary fields
- Compressed responses for large datasets
- CDN caching for static configuration data

## Localization Support

### Multi-language Support
The strings system supports localization for different languages:

```json
{
  "name": {
    "en": "Draft",
    "es": "Borrador",
    "fr": "Brouillon"
  }
}
```

### Regional Variations
Support for regional variations in terminology and formatting.

## Error Handling

### Common Errors
- **Category Not Found**: Requested category doesn't exist
- **Invalid Format**: Malformed request parameters
- **Server Error**: Internal server issues

### Error Response Format
```json
{
  "error": "CategoryNotFound",
  "message": "The requested string category does not exist",
  "status": 404,
  "details": {
    "category": "invalidCategory",
    "availableCategories": ["applicationStatuses", "awardStatuses", "clientTypes"]
  }
}
```

## Integration Points

### All Modules
The strings API is used by all modules for:
- Status definitions and display
- Category and type definitions
- Validation rules and constraints
- System configuration values

### Frontend Components
- Form components for validation
- Status display components
- Filter and dropdown components
- Configuration-driven UI elements

### Backend Services
- Data validation services
- Business rule enforcement
- Configuration-driven logic
- Internationalization services
