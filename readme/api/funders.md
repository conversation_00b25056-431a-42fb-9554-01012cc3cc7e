# Funders API

## Overview
The Funders API provides search and retrieval functionality for funding organizations. It allows users to search for potential funders and retrieve funder information for grant applications.

## Base Route
`/funders`

## Authentication
All endpoints require authentication via JWT token in the Authorization header.

## Endpoints

### Search Funders

#### GET /funders/search
Searches for funding organizations using various criteria.

**Query Parameters:**
- `q`: Search query string (searches across name, description, focus areas)
- `type`: Funder type ('government', 'foundation', 'corporate', 'nonprofit')
- `focusArea`: Focus area or category ('health', 'education', 'environment', etc.)
- `location`: Geographic location (state, city, or region)
- `fundingRange`: Funding amount range ('small', 'medium', 'large', 'custom')
- `minAmount`: Minimum funding amount (for custom range)
- `maxAmount`: Maximum funding amount (for custom range)
- `eligibility`: Eligibility criteria ('501c3', 'government', 'individual', 'forprofit')
- `applicationDeadline`: Filter by upcoming deadlines ('30days', '60days', '90days')
- `limit`: Maximum results to return (default: 20)
- `offset`: Number of results to skip (default: 0)

**Response:**
```json
{
  "data": {
    "funders": [
      {
        "id": 1,
        "name": "National Health Foundation",
        "type": "foundation",
        "description": "Private foundation supporting health research and community health initiatives",
        "website": "https://nationalhealthfoundation.org",
        "focusAreas": ["health", "medical research", "community health"],
        "location": {
          "city": "Washington",
          "state": "DC",
          "country": "USA"
        },
        "fundingRange": {
          "min": 10000,
          "max": 500000,
          "typical": 75000
        },
        "eligibilityRequirements": [
          "501(c)(3) nonprofit organizations",
          "Educational institutions",
          "Government agencies"
        ],
        "applicationDeadlines": [
          {
            "program": "Community Health Grants",
            "deadline": "2024-03-15",
            "amount": 100000
          }
        ],
        "contactInfo": {
          "email": "<EMAIL>",
          "phone": "+1-************",
          "address": "123 Foundation Way, Washington, DC 20001"
        },
        "lastUpdated": "2024-01-20T10:00:00Z",
        "relevanceScore": 0.95
      },
      {
        "id": 2,
        "name": "Department of Health and Human Services",
        "type": "government",
        "description": "Federal agency providing health-related grants and funding",
        "website": "https://hhs.gov/grants",
        "focusAreas": ["health", "human services", "public health"],
        "location": {
          "city": "Washington",
          "state": "DC",
          "country": "USA"
        },
        "fundingRange": {
          "min": 50000,
          "max": 5000000,
          "typical": 250000
        },
        "eligibilityRequirements": [
          "State and local governments",
          "Nonprofit organizations",
          "Educational institutions",
          "Tribal organizations"
        ],
        "applicationDeadlines": [
          {
            "program": "Community Health Centers",
            "deadline": "2024-04-30",
            "amount": 1000000
          }
        ],
        "contactInfo": {
          "email": "<EMAIL>",
          "phone": "+1-************"
        },
        "lastUpdated": "2024-01-22T14:30:00Z",
        "relevanceScore": 0.88
      }
    ],
    "pagination": {
      "total": 156,
      "limit": 20,
      "offset": 0,
      "hasMore": true
    },
    "searchMetadata": {
      "query": "health foundation",
      "executionTime": 0.045,
      "totalResults": 156,
      "filters": {
        "type": "foundation",
        "focusArea": "health"
      }
    }
  }
}
```

### Get Funder by ID

#### GET /funders/:id
Retrieves detailed information for a specific funder.

**Path Parameters:**
- `id`: Funder ID

**Response:**
```json
{
  "data": {
    "funder": {
      "id": 1,
      "name": "National Health Foundation",
      "type": "foundation",
      "description": "Private foundation supporting health research and community health initiatives nationwide",
      "website": "https://nationalhealthfoundation.org",
      "foundedYear": 1985,
      "focusAreas": ["health", "medical research", "community health", "health education"],
      "location": {
        "city": "Washington",
        "state": "DC",
        "country": "USA",
        "region": "Mid-Atlantic"
      },
      "fundingRange": {
        "min": 10000,
        "max": 500000,
        "typical": 75000,
        "totalAnnual": 15000000
      },
      "eligibilityRequirements": [
        "501(c)(3) nonprofit organizations",
        "Educational institutions",
        "Government agencies",
        "Faith-based organizations"
      ],
      "applicationRequirements": [
        "Letter of inquiry required",
        "Full proposal if invited",
        "Budget and timeline",
        "Letters of support"
      ],
      "fundingPrograms": [
        {
          "name": "Community Health Grants",
          "description": "Grants for community-based health initiatives",
          "maxAmount": 100000,
          "deadline": "2024-03-15",
          "applicationOpen": true
        },
        {
          "name": "Health Research Awards",
          "description": "Support for innovative health research projects",
          "maxAmount": 250000,
          "deadline": "2024-06-30",
          "applicationOpen": true
        }
      ],
      "contactInfo": {
        "primaryContact": {
          "name": "Dr. Sarah Johnson",
          "title": "Program Director",
          "email": "<EMAIL>",
          "phone": "+1-************"
        },
        "generalContact": {
          "email": "<EMAIL>",
          "phone": "+1-************"
        },
        "address": {
          "street": "123 Foundation Way",
          "city": "Washington",
          "state": "DC",
          "zipCode": "20001",
          "country": "USA"
        }
      },
      "applicationProcess": {
        "steps": [
          "Submit letter of inquiry",
          "Invitation to submit full proposal",
          "Review by expert panel",
          "Board decision",
          "Award notification"
        ],
        "timeline": "4-6 months",
        "decisionNotification": "Email and postal mail"
      },
      "reportingRequirements": [
        "Interim progress reports",
        "Final report within 90 days",
        "Financial reporting",
        "Impact assessment"
      ],
      "preferences": {
        "geographicFocus": ["United States"],
        "organizationSize": ["small", "medium", "large"],
        "projectDuration": "1-3 years",
        "collaborationEncouraged": true
      },
      "statistics": {
        "grantsAwarded2023": 45,
        "totalFunding2023": 3200000,
        "averageGrant2023": 71111,
        "successRate": 0.18
      },
      "lastUpdated": "2024-01-20T10:00:00Z",
      "dataSource": "Foundation Directory Online",
      "verified": true
    }
  }
}
```

### Get Funder Categories

#### GET /funders/categories
Retrieves available funder categories and focus areas.

**Response:**
```json
{
  "data": {
    "categories": {
      "types": [
        {
          "id": "government",
          "name": "Government",
          "description": "Federal, state, and local government agencies",
          "count": 245
        },
        {
          "id": "foundation",
          "name": "Foundation",
          "description": "Private and community foundations",
          "count": 1856
        },
        {
          "id": "corporate",
          "name": "Corporate",
          "description": "Corporate giving programs and foundations",
          "count": 432
        },
        {
          "id": "nonprofit",
          "name": "Nonprofit",
          "description": "Nonprofit organizations providing grants",
          "count": 178
        }
      ],
      "focusAreas": [
        {
          "id": "health",
          "name": "Health",
          "subcategories": ["public health", "medical research", "mental health", "healthcare access"],
          "count": 567
        },
        {
          "id": "education",
          "name": "Education",
          "subcategories": ["k12", "higher education", "early childhood", "adult education"],
          "count": 623
        },
        {
          "id": "environment",
          "name": "Environment",
          "subcategories": ["conservation", "climate change", "sustainability", "wildlife"],
          "count": 289
        },
        {
          "id": "arts",
          "name": "Arts & Culture",
          "subcategories": ["visual arts", "performing arts", "museums", "cultural preservation"],
          "count": 234
        }
      ],
      "fundingRanges": [
        {
          "id": "small",
          "name": "Small Grants",
          "range": "Under $25,000",
          "count": 892
        },
        {
          "id": "medium",
          "name": "Medium Grants",
          "range": "$25,000 - $100,000",
          "count": 1245
        },
        {
          "id": "large",
          "name": "Large Grants",
          "range": "Over $100,000",
          "count": 567
        }
      ]
    }
  }
}
```

### Get Trending Funders

#### GET /funders/trending
Retrieves currently trending or popular funders.

**Query Parameters:**
- `period`: Time period ('week', 'month', 'quarter')
- `category`: Filter by category
- `limit`: Maximum results to return

**Response:**
```json
{
  "data": {
    "trending": [
      {
        "id": 1,
        "name": "National Health Foundation",
        "type": "foundation",
        "trendingReason": "New grant program announced",
        "searchCount": 156,
        "changePercent": 45.2,
        "focusAreas": ["health", "medical research"]
      },
      {
        "id": 3,
        "name": "Education Innovation Fund",
        "type": "foundation",
        "trendingReason": "Application deadline approaching",
        "searchCount": 134,
        "changePercent": 38.7,
        "focusAreas": ["education", "technology"]
      }
    ],
    "period": "month",
    "generatedAt": "2024-01-25T10:00:00Z"
  }
}
```

### Save Funder

#### POST /funders/save
Saves a funder to the user's saved list.

**Request Body:**
```json
{
  "funderId": 1,
  "notes": "Potential funder for community health project",
  "tags": ["health", "community", "priority"]
}
```

**Response:**
```json
{
  "data": {
    "saved": {
      "id": 123,
      "funderId": 1,
      "userId": 2,
      "notes": "Potential funder for community health project",
      "tags": ["health", "community", "priority"],
      "savedAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Funder saved successfully"
}
```

### Get Saved Funders

#### GET /funders/saved
Retrieves the user's saved funders.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `tags`: Filter by tags
- `sortBy`: Sort field ('savedAt', 'name', 'relevance')

**Response:**
```json
{
  "data": {
    "savedFunders": [
      {
        "id": 123,
        "funder": {
          "id": 1,
          "name": "National Health Foundation",
          "type": "foundation",
          "focusAreas": ["health", "medical research"]
        },
        "notes": "Potential funder for community health project",
        "tags": ["health", "community", "priority"],
        "savedAt": "2024-01-25T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "pages": 1
    }
  }
}
```

## Search Features

### Advanced Search
- **Full-text Search**: Search across name, description, and focus areas
- **Filtered Search**: Combine multiple filter criteria
- **Geographic Search**: Search by location and region
- **Deadline Search**: Find funders with upcoming deadlines
- **Amount Range Search**: Search by funding amount ranges

### Search Relevance
- **Relevance Scoring**: Rank results by relevance to query
- **Personalization**: Personalized results based on user history
- **Trending Boost**: Boost trending funders in results
- **Quality Scoring**: Prioritize verified and complete profiles

### Search Analytics
- **Search Tracking**: Track popular search terms
- **Result Analytics**: Monitor search result effectiveness
- **User Behavior**: Analyze user search patterns
- **Performance Metrics**: Search performance optimization

## Data Sources and Updates

### Data Sources
- **Foundation Directory Online**: Comprehensive foundation database
- **Government Grants**: Federal and state grant databases
- **Corporate Giving**: Corporate foundation and giving programs
- **Manual Curation**: Manually curated and verified data

### Data Updates
- **Automated Updates**: Regular automated data synchronization
- **Manual Updates**: Staff-curated updates and corrections
- **User Contributions**: User-submitted updates and corrections
- **Verification Process**: Data verification and quality control

### Data Quality
- **Verification Status**: Verified vs. unverified data
- **Freshness Indicators**: Data age and last update timestamps
- **Completeness Scores**: Profile completeness ratings
- **Accuracy Monitoring**: Ongoing accuracy monitoring

## Integration Points

### Applications Module
- **Funder Selection**: Select funders for applications
- **Funder Matching**: Match applications to suitable funders
- **Application Tracking**: Track applications by funder

### Programs Module
- **Program Funders**: Associate programs with potential funders
- **Funding Opportunities**: Match programs to funding opportunities
- **Funder Requirements**: Align program requirements with funder criteria

### Reports Module
- **Funder Analytics**: Analyze funder success rates
- **Funding Trends**: Track funding trends and patterns
- **Success Metrics**: Measure funder relationship success

## Error Handling

### Common Errors
- **Search Errors**: Invalid search parameters
- **Not Found**: Funder not found
- **Rate Limiting**: Search rate limits exceeded
- **Data Unavailable**: External data source unavailable

### Error Response Format
```json
{
  "error": "FunderSearchError",
  "message": "Invalid search parameters provided",
  "status": 400,
  "details": {
    "parameter": "fundingRange",
    "value": "invalid-range",
    "expected": "small, medium, large, or custom"
  }
}
```

## Performance and Caching

### Search Performance
- **Indexed Search**: Optimized search indexes
- **Query Optimization**: Efficient search queries
- **Result Caching**: Cache popular search results
- **Pagination**: Efficient result pagination

### Data Caching
- **Funder Profiles**: Cache complete funder profiles
- **Search Results**: Cache search result sets
- **Category Data**: Cache category and filter data
- **Trending Data**: Cache trending funder data
