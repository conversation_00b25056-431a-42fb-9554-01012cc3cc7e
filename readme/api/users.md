# Users API

## Overview
The Users API manages user accounts, permissions, and access control throughout the admin panel. It handles user creation, editing, role assignments, client relationships, and authentication workflows.

## Base Route
`/users`

## Authentication
Most endpoints require authentication via <PERSON>W<PERSON> token in the Authorization header.

## Endpoints

### Get Current User

#### GET /users
Retrieves the current authenticated user's information.

**Authentication Required**: Yes

**Response:**
```json
{
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "userType": "millenniumAdmin",
      "position": "Administrator",
      "phone": "+**********",
      "location": "New York, NY",
      "clientCreatorId": null,
      "defaultRole": "admin",
      "isActive": true,
      "createdAt": "2024-01-01T10:00:00Z",
      "lastLoginAt": "2024-01-25T09:30:00Z",
      "clients": [
        {
          "id": 1,
          "name": "Health Organization",
          "role": "admin"
        }
      ]
    }
  }
}
```

### Search Users

#### GET /users/search
Searches users using text query and filters.

**Authentication Required**: Yes

**Query Parameters:**
- `q`: Search query string
- `userType`: Filter by user type
- `clientId`: Filter by client ID
- `isActive`: Filter by active status
- `limit`: Maximum results to return

**Response:**
```json
{
  "data": {
    "users": [
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "userType": "clientUser",
        "position": "Manager",
        "isActive": true,
        "client": {
          "id": 1,
          "name": "Health Organization"
        }
      }
    ]
  }
}
```

### Get Read Users

#### GET /users/read
Retrieves users for read/assignment purposes.

**Authentication Required**: Yes

**Query Parameters:**
- `clientId`: Filter by client ID
- `userType`: Filter by user type

**Response:**
```json
{
  "data": {
    "users": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "userType": "millenniumAdmin"
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "userType": "clientUser"
      }
    ]
  }
}
```

### Create User

#### POST /users
Creates a new user account.

**Authentication Required**: Yes (for admin creation) / No (for public registration)
**Permission**: `employee:create` (for admin creation)

**Request Body:**
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "secure_password",
  "passwordConfirmation": "secure_password",
  "userType": "clientUser",
  "position": "Analyst",
  "phone": "+**********",
  "location": "Boston, MA",
  "clientCreatorId": 1,
  "defaultRole": "user",
  "assignedClients": [1, 2]
}
```

**Response:**
```json
{
  "data": {
    "user": {
      "id": 3,
      "name": "New User",
      "email": "<EMAIL>",
      "userType": "clientUser",
      "isActive": true,
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "User created successfully"
}
```

### Update User

#### PUT /users
Updates an existing user account.

**Authentication Required**: Yes
**Permission**: `employee:update`

**Request Body:**
```json
{
  "id": 2,
  "name": "Updated Name",
  "position": "Senior Manager",
  "phone": "+**********",
  "location": "Chicago, IL",
  "userType": "userAdmin",
  "isActive": true,
  "assignedClients": [1, 3]
}
```

**Response:**
```json
{
  "data": {
    "user": {
      "id": 2,
      "name": "Updated Name",
      "position": "Senior Manager",
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "User updated successfully"
}
```

### Delete User

#### DELETE /users
Deletes a user account.

**Authentication Required**: Yes
**Permission**: `employee:delete`

**Request Body:**
```json
{
  "id": 2
}
```

**Response:**
```json
{
  "message": "User deleted successfully"
}
```

## Password Management

### Create Password

#### POST /users/password
Creates a password for a user (typically for new accounts).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "new_password",
  "passwordConfirmation": "new_password",
  "token": "password_creation_token"
}
```

**Response:**
```json
{
  "message": "Password created successfully"
}
```

### Change Password

#### PATCH /users/password
Changes a user's password (for password reset).

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "new_password",
  "passwordConfirmation": "new_password"
}
```

**Response:**
```json
{
  "message": "Password changed successfully"
}
```

## User Invitation System

### Verify Invitation

#### GET /users/invite
Verifies an invitation token and returns invitation details.

**Query Parameters:**
- `token`: Invitation token

**Response:**
```json
{
  "data": {
    "invitation": {
      "email": "<EMAIL>",
      "name": "Invited User",
      "userType": "clientUser",
      "clientId": 1,
      "position": "Analyst",
      "isValid": true,
      "expiresAt": "2024-02-01T10:00:00Z"
    }
  }
}
```

### Send Invitations

#### POST /users/invite
Sends invitation emails to new users.

**Authentication Required**: Yes
**Permission**: `employee:invite`

**Request Body:**
```json
{
  "invitations": [
    {
      "name": "Invited User 1",
      "email": "<EMAIL>",
      "userType": "clientUser",
      "position": "Analyst",
      "clientId": 1
    },
    {
      "name": "Invited User 2",
      "email": "<EMAIL>",
      "userType": "userAdmin",
      "position": "Manager",
      "clientId": 2
    }
  ]
}
```

**Response:**
```json
{
  "data": {
    "sent": 2,
    "failed": 0,
    "results": [
      {
        "email": "<EMAIL>",
        "status": "sent",
        "invitationId": "inv_123"
      },
      {
        "email": "<EMAIL>",
        "status": "sent",
        "invitationId": "inv_124"
      }
    ]
  },
  "message": "Invitations sent successfully"
}
```

### Create Invited User

#### POST /users/create-invited-user
Creates a user account from an invitation token.

**Request Body:**
```json
{
  "token": "invitation_token",
  "password": "user_password",
  "passwordConfirmation": "user_password"
}
```

**Response:**
```json
{
  "data": {
    "user": {
      "id": 4,
      "name": "Invited User",
      "email": "<EMAIL>",
      "userType": "clientUser",
      "isActive": true
    }
  },
  "message": "User account created successfully"
}
```

## User Types and Roles

### User Types
- **millenniumAdmin**: Full system access and administration
- **millenniumManager**: Broad management capabilities
- **millenniumAnalyst**: Analysis and reporting access
- **userAdmin**: Client-specific user management
- **clientAdmin**: Client organization administration
- **clientAnalyst**: Client-specific analysis and reporting

### Role Management
Users can have different roles for different clients:
- **admin**: Full administrative access
- **manager**: Management capabilities
- **user**: Standard user access
- **analyst**: Analysis and reporting access
- **viewer**: Read-only access

## Client Assignments

### User-Client Relationships
Users can be assigned to multiple clients with different roles:

```json
{
  "userClientAssignments": [
    {
      "userId": 2,
      "clientId": 1,
      "role": "admin",
      "canCreateApplications": true,
      "canManageAwards": true
    },
    {
      "userId": 2,
      "clientId": 2,
      "role": "user",
      "canCreateApplications": false,
      "canManageAwards": false
    }
  ]
}
```

### Client-Specific Permissions
- **Application Access**: Which applications user can view/edit
- **Award Access**: Which awards user can manage
- **User Management**: Whether user can manage other users
- **Reporting Access**: What reports user can access

## User Statistics and Analytics

### User Activity Tracking
- Login/logout events
- Application and award interactions
- Report generation and access
- File uploads and downloads

### User Performance Metrics
- Applications processed
- Awards managed
- Response times
- Productivity metrics

## Validation and Business Rules

### User Data Validation
- **Email Validation**: Unique email addresses
- **Password Requirements**: Strength and complexity rules
- **User Type Validation**: Valid user type assignments
- **Client Assignment Validation**: Valid client relationships

### Business Rules
- **User Type Restrictions**: What each user type can do
- **Client Scope Limitations**: Users limited to assigned clients
- **Permission Inheritance**: Role-based permission inheritance
- **Account Activation**: Email verification requirements

## Security Features

### Account Security
- **Password Hashing**: Secure password storage
- **Account Lockout**: Protection against brute force attacks
- **Session Management**: Secure session handling
- **Two-Factor Authentication**: Optional 2FA support

### Access Control
- **Role-Based Access**: Granular permission system
- **Client Isolation**: Users see only authorized data
- **API Rate Limiting**: Protection against abuse
- **Audit Logging**: Track all user actions

## Error Handling

### Common Errors
- **Validation Errors**: Invalid user data
- **Permission Errors**: Insufficient access rights
- **Duplicate Email**: Email already exists
- **Invalid User Type**: Invalid user type assignment
- **Client Assignment Errors**: Invalid client relationships

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Email address is already in use",
  "status": 400,
  "details": {
    "field": "email",
    "value": "<EMAIL>"
  }
}
```

## Integration Points

### Client Management
- User-client relationship management
- Client-specific user permissions
- Client user creation and management

### Applications and Awards
- User assignment to applications and awards
- Role-based access to applications and awards
- User performance tracking

### Notifications
- User notification preferences
- Email notification delivery
- System announcement distribution

### Reporting
- User activity reporting
- User performance analytics
- Access and usage statistics
