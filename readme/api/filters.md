# Filters API

## Overview
The Filters API manages saved filter configurations for search and data filtering across the grant management system. It allows users to save, share, and reuse complex filter combinations for applications, awards, clients, and other entities.

## Base Route
`/filters`

## Authentication
All endpoints require authentication via <PERSON>W<PERSON> token in the Authorization header.

## Endpoints

### List Filters

#### GET /filters
Retrieves a list of saved filters for the current user.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `entityType`: Filter by entity type ('application', 'award', 'client', 'program')
- `isPublic`: Filter by public/private status
- `createdBy`: Filter by creator user ID
- `sortBy`: Sort field ('name', 'createdAt', 'lastUsed')
- `sortOrder`: Sort order ('asc', 'desc')

**Response:**
```json
{
  "data": {
    "filters": [
      {
        "id": 1,
        "name": "Active Health Applications",
        "description": "Applications in health category with active status",
        "entityType": "application",
        "filterCriteria": {
          "status": ["submitted", "under_review"],
          "category": ["health"],
          "dateRange": {
            "field": "submissionDate",
            "start": "2024-01-01",
            "end": "2024-12-31"
          }
        },
        "isPublic": false,
        "isDefault": false,
        "usageCount": 15,
        "lastUsedAt": "2024-01-24T10:00:00Z",
        "createdAt": "2024-01-01T10:00:00Z",
        "createdBy": {
          "id": 2,
          "name": "John Doe"
        },
        "sharedWith": []
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 8,
      "pages": 1
    }
  }
}
```

### Get Filter by ID

#### GET /filters/:id
Retrieves detailed information for a specific filter.

**Path Parameters:**
- `id`: Filter ID

**Response:**
```json
{
  "data": {
    "filter": {
      "id": 1,
      "name": "Active Health Applications",
      "description": "Applications in health category with active status",
      "entityType": "application",
      "filterCriteria": {
        "status": ["submitted", "under_review"],
        "category": ["health"],
        "clientId": [1, 2, 3],
        "assigneeId": [5, 6],
        "fundingAmountRange": {
          "min": 10000,
          "max": 100000
        },
        "dateRange": {
          "field": "submissionDate",
          "start": "2024-01-01",
          "end": "2024-12-31"
        },
        "customFields": {
          "priority": ["high", "medium"]
        }
      },
      "displayOptions": {
        "columns": ["name", "status", "fundingAmount", "submissionDate"],
        "sortBy": "submissionDate",
        "sortOrder": "desc"
      },
      "isPublic": false,
      "isDefault": false,
      "usageCount": 15,
      "lastUsedAt": "2024-01-24T10:00:00Z",
      "createdAt": "2024-01-01T10:00:00Z",
      "updatedAt": "2024-01-15T14:30:00Z",
      "createdBy": {
        "id": 2,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "sharedWith": [
        {
          "userId": 3,
          "userName": "Jane Smith",
          "permissions": ["read", "use"]
        }
      ]
    }
  }
}
```

### Create Filter

#### POST /filters
Creates a new saved filter.

**Request Body:**
```json
{
  "name": "High Priority Awards",
  "description": "Awards with high priority and active status",
  "entityType": "award",
  "filterCriteria": {
    "status": ["active"],
    "customFields": {
      "priority": ["high"]
    },
    "awardAmountRange": {
      "min": 50000
    },
    "dateRange": {
      "field": "startDate",
      "start": "2024-01-01"
    }
  },
  "displayOptions": {
    "columns": ["name", "status", "awardAmount", "startDate"],
    "sortBy": "awardAmount",
    "sortOrder": "desc"
  },
  "isPublic": false,
  "isDefault": false
}
```

**Response:**
```json
{
  "data": {
    "filter": {
      "id": 2,
      "name": "High Priority Awards",
      "entityType": "award",
      "isPublic": false,
      "createdAt": "2024-01-25T10:00:00Z",
      "createdBy": {
        "id": 2,
        "name": "John Doe"
      }
    }
  },
  "message": "Filter created successfully"
}
```

### Update Filter

#### PUT /filters/:id
Updates an existing filter.

**Path Parameters:**
- `id`: Filter ID

**Request Body:**
```json
{
  "name": "Updated Filter Name",
  "description": "Updated description",
  "filterCriteria": {
    "status": ["active", "completed"],
    "customFields": {
      "priority": ["high", "critical"]
    }
  },
  "isPublic": true
}
```

**Response:**
```json
{
  "data": {
    "filter": {
      "id": 1,
      "name": "Updated Filter Name",
      "description": "Updated description",
      "updatedAt": "2024-01-25T15:00:00Z"
    }
  },
  "message": "Filter updated successfully"
}
```

### Delete Filter

#### DELETE /filters/:id
Deletes a saved filter.

**Path Parameters:**
- `id`: Filter ID

**Response:**
```json
{
  "message": "Filter deleted successfully"
}
```

### Search Filters

#### GET /filters/search
Searches filters by name and description.

**Query Parameters:**
- `q`: Search query string
- `entityType`: Filter by entity type
- `includePublic`: Include public filters in results
- `limit`: Maximum results to return

**Response:**
```json
{
  "data": {
    "filters": [
      {
        "id": 1,
        "name": "Active Health Applications",
        "description": "Applications in health category with active status",
        "entityType": "application",
        "relevanceScore": 0.95,
        "createdBy": {
          "id": 2,
          "name": "John Doe"
        }
      }
    ]
  }
}
```

### Apply Filter

#### POST /filters/:id/apply
Applies a saved filter and returns filtered results.

**Path Parameters:**
- `id`: Filter ID

**Query Parameters:**
- `page`: Page number for results
- `limit`: Items per page
- `additionalFilters`: Additional filter criteria (JSON)

**Response:**
```json
{
  "data": {
    "results": [
      {
        "id": 1,
        "name": "Community Health Program",
        "status": "submitted",
        "category": "health",
        "fundingAmount": 50000
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    },
    "appliedFilter": {
      "id": 1,
      "name": "Active Health Applications"
    }
  }
}
```

### Share Filter

#### POST /filters/:id/share
Shares a filter with other users.

**Path Parameters:**
- `id`: Filter ID

**Request Body:**
```json
{
  "userIds": [3, 4, 5],
  "permissions": ["read", "use"],
  "message": "Sharing this useful filter for health applications"
}
```

**Response:**
```json
{
  "data": {
    "shared": 3,
    "failed": 0,
    "results": [
      {
        "userId": 3,
        "status": "shared",
        "permissions": ["read", "use"]
      },
      {
        "userId": 4,
        "status": "shared",
        "permissions": ["read", "use"]
      },
      {
        "userId": 5,
        "status": "shared",
        "permissions": ["read", "use"]
      }
    ]
  },
  "message": "Filter shared successfully"
}
```

### Clone Filter

#### POST /filters/:id/clone
Creates a copy of an existing filter.

**Path Parameters:**
- `id`: Filter ID

**Request Body:**
```json
{
  "name": "My Copy of Active Health Applications",
  "description": "Personal copy with modifications"
}
```

**Response:**
```json
{
  "data": {
    "filter": {
      "id": 3,
      "name": "My Copy of Active Health Applications",
      "description": "Personal copy with modifications",
      "createdAt": "2024-01-25T10:00:00Z"
    }
  },
  "message": "Filter cloned successfully"
}
```

## Filter Criteria Structure

### Common Filter Fields
- **Text Fields**: name, description, notes
- **Status Fields**: status, stage, phase
- **Date Fields**: createdAt, updatedAt, submissionDate, dueDate
- **Numeric Fields**: amounts, counts, scores
- **Reference Fields**: clientId, userId, programId
- **Custom Fields**: entity-specific custom fields

### Filter Operators
- **Equals**: Exact match
- **Contains**: Partial text match
- **In**: Value in list
- **Range**: Between min and max values
- **Greater Than**: Numeric comparison
- **Less Than**: Numeric comparison
- **Date Range**: Between start and end dates
- **Is Null**: Field is empty
- **Is Not Null**: Field has value

### Complex Filter Examples

#### Application Filter
```json
{
  "filterCriteria": {
    "status": ["submitted", "under_review"],
    "category": ["health", "education"],
    "fundingAmountRange": {
      "min": 10000,
      "max": 100000
    },
    "submissionDateRange": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    },
    "clientId": [1, 2, 3],
    "assigneeId": [5, 6],
    "customFields": {
      "priority": ["high", "medium"],
      "region": ["northeast", "southeast"]
    },
    "textSearch": {
      "query": "community health",
      "fields": ["name", "description"]
    }
  }
}
```

#### Award Filter
```json
{
  "filterCriteria": {
    "status": ["active", "completed"],
    "awardAmountRange": {
      "min": 25000
    },
    "startDateRange": {
      "start": "2024-01-01"
    },
    "clientType": ["full", "flex"],
    "hasPayments": true,
    "customFields": {
      "fundingSource": ["federal", "state"]
    }
  }
}
```

## Filter Sharing and Permissions

### Permission Types
- **read**: View filter details
- **use**: Apply filter to get results
- **edit**: Modify filter criteria
- **share**: Share filter with others
- **delete**: Remove filter

### Sharing Levels
- **Private**: Only creator can access
- **Shared**: Shared with specific users
- **Team**: Shared with team members
- **Public**: Available to all users
- **Organization**: Available to organization members

### Access Control
- **Owner Permissions**: Full control over filter
- **Shared Permissions**: Limited based on granted permissions
- **Public Permissions**: Read and use only
- **Admin Override**: Admins can access all filters

## Filter Templates and Presets

### System Templates
- **Default Filters**: Pre-configured common filters
- **Entity Templates**: Templates for each entity type
- **Role-based Templates**: Templates based on user roles
- **Industry Templates**: Industry-specific filter templates

### Custom Templates
- **Organization Templates**: Organization-specific templates
- **Team Templates**: Team-specific filter templates
- **Personal Templates**: User-created templates
- **Shared Templates**: Community-shared templates

## Performance and Optimization

### Filter Optimization
- **Query Optimization**: Efficient database queries
- **Index Usage**: Leverage database indexes
- **Caching**: Cache frequently used filters
- **Result Caching**: Cache filter results

### Usage Analytics
- **Filter Usage**: Track filter usage frequency
- **Performance Metrics**: Monitor filter performance
- **Popular Filters**: Identify most used filters
- **Optimization Suggestions**: Suggest filter improvements

## Error Handling

### Common Errors
- **Invalid Criteria**: Invalid filter criteria
- **Permission Denied**: Insufficient access rights
- **Filter Not Found**: Filter doesn't exist
- **Sharing Errors**: Issues sharing filters
- **Application Errors**: Errors applying filters

### Error Response Format
```json
{
  "error": "FilterValidationError",
  "message": "Invalid date range in filter criteria",
  "status": 400,
  "details": {
    "field": "dateRange.start",
    "value": "invalid-date",
    "expected": "ISO 8601 date format"
  }
}
```

## Integration Points

### Applications Module
- Application-specific filter criteria
- Application status and category filters
- Application date and amount filters

### Awards Module
- Award-specific filter criteria
- Award status and type filters
- Award financial filters

### Clients Module
- Client-specific filter criteria
- Client type and status filters
- Client geographic filters

### Programs Module
- Program-specific filter criteria
- Program category and status filters
- Program eligibility filters

### Reports Module
- Filter-based report generation
- Filter criteria in report parameters
- Saved filters for recurring reports
