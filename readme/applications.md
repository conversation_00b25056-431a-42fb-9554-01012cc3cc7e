# Applications Module

## Overview
The Applications module manages grant applications throughout their lifecycle, from initial creation to final award status. It provides comprehensive application tracking, editing capabilities, bulk operations, and integration with the awards system.

## Main Features

### Applications List
- **Route**: `/dashboard/applications`
- **Purpose**: Display and manage all grant applications
- **Features**:
  - Comprehensive applications table with key information
  - Advanced filtering and search capabilities
  - Bulk operations and multiple selection
  - Application status management
  - Export functionality
  - Pagination and sorting

### Application Editing
- **Route**: `/dashboard/applications/edit/:applicationId`
- **Purpose**: Edit individual application details
- **Features**:
  - Complete application information editing
  - Status change management
  - File attachments and document management
  - Custom fields configuration
  - Award creation integration

### Multiple Application Editing
- **Route**: `/dashboard/applications/edit/multiple`
- **Purpose**: Bulk edit multiple applications simultaneously
- **Features**:
  - Bulk status changes
  - Bulk field updates
  - Mass assignment operations
  - Bulk export and reporting

## Application Information Management

### Basic Application Data
- **Application Name**: Descriptive name for the grant application
- **Program Assignment**: Link to specific funding program
- **Client Assignment**: Associated client organization
- **Assignee**: User responsible for the application
- **Funder**: Funding organization or source
- **Funding Amount**: Requested or awarded amount
- **Application Status**: Current stage in the process

### Application Status Types
- **Draft**: Initial creation stage
- **Submitted**: Application has been submitted
- **Under Review**: Currently being evaluated
- **Awarded**: Application has been approved for funding
- **Rejected**: Application was not approved
- **Withdrawn**: Application was withdrawn by applicant

### Dates and Timeline
- **Start Date**: Project start date
- **End Date**: Project completion date
- **Award Date**: Date award was granted
- **Notification Date**: Date applicant was notified
- **Submission Date**: Date application was submitted

### Financial Information
- **Funding Amount**: Primary funding amount
- **Varying Funding Amount**: Text description for variable amounts
- **Amount Varies**: Boolean flag for variable funding
- **Match Requirements**: Required matching funds
- **Performance Period**: Duration of funded activities

## Award Creation Process

### Overview
When an application's status is changed to "Awarded", the system can automatically generate an award record in the Awards module. This process follows specific rules and displays notifications to users.

### Current Behavior
1. **Automatic Award Creation Conditions:**
   - Application status is changed to "Awarded" (status code 4)
   - Client has awards enabled (`client.awardsEnabled === true`)
   - Application has a funding amount specified
   - Application does not have varying funding amount (`varyingFundingAmount === false`)

2. **User Notifications:**
   - If an award already exists for the application:
     - A confirmation popup asks: "An award record already exists for this application. Would you like to update it?"
     - If confirmed, the existing award is updated
   - If no award exists:
     - A confirmation dialog asks: "Would you like to create an award record for this application in the Awards module?"
     - The award is only created if the user selects "Yes"

3. **Technical Implementation:**
   - The notification logic is in `src/components/applications/ApplicationEdit.tsx`
   - The actual award creation happens in `src/helpers/applications/create-award.helper.ts`
   - The `createAwardFromApplication` function handles both creating new awards and updating existing ones
   - The `generateAward` flag controls whether an award should be created or not

## Custom Fields and Configuration

### Custom Fields System
- **Dynamic Fields**: Client-specific custom fields
- **Field Types**: Text, number, date, dropdown, checkbox
- **Field Validation**: Custom validation rules
- **Field Dependencies**: Conditional field display
- **Field History**: Track changes to custom field values

### Application Categories and Sources
- **Category Classification**: Organize applications by type
- **Source Tracking**: Track application origin and referral source
- **Department Notification**: Notify relevant departments
- **Grant Purpose**: Define the purpose and goals of the grant

## File Management

### Document Attachments
- **File Upload**: Support for various document types
- **File Versioning**: Track document revisions
- **File Access Control**: Role-based file access
- **Bulk File Operations**: Download multiple files

### Summary Files
- **Application Summaries**: Generate application summary documents
- **Template System**: Use standardized summary templates
- **Export Options**: Multiple export formats
- **Automated Generation**: Auto-generate summaries

## Filtering and Search

### Advanced Filters
- **Status Filter**: Filter by application status
- **Client Filter**: Filter by client organization
- **Assignee Filter**: Filter by assigned users
- **Date Filters**: Filter by various date ranges
- **Program Filter**: Filter by funding program
- **Amount Filter**: Filter by funding amount ranges

### Search Capabilities
- **Text Search**: Search across application fields
- **Quick Filters**: Predefined filter combinations
- **Saved Filters**: Save and reuse filter combinations
- **Export Filtered Data**: Export based on current filters

## Bulk Operations

### Multiple Selection
- **Checkbox Selection**: Select multiple applications
- **Select All**: Select all visible applications
- **Select by Filter**: Select applications matching criteria
- **Selection Persistence**: Maintain selections across pages

### Bulk Actions
- **Status Changes**: Change status for multiple applications
- **Assignee Changes**: Reassign multiple applications
- **Bulk Export**: Export selected applications
- **Bulk Delete**: Remove multiple applications
- **Bulk Email**: Send notifications to multiple applicants

## Integration Points

### Programs Module
- **Program Assignment**: Link applications to funding programs
- **Program Eligibility**: Check program requirements
- **Program Visibility**: Show relevant programs to clients
- **Program Statistics**: Track program application rates

### Clients Module
- **Client Assignment**: Link applications to client organizations
- **Client-specific Applications**: Filter by client access
- **Client Permissions**: Control client application access
- **Client Statistics**: Track client application performance

### Users Module
- **User Assignment**: Assign applications to users
- **User Permissions**: Control user application access
- **User Workload**: Track user application assignments
- **User Performance**: Monitor user application processing

### Awards Module
- **Award Creation**: Automatic award generation from applications
- **Award Synchronization**: Keep application and award data in sync
- **Award Status**: Reflect award status in applications
- **Award Integration**: Seamless transition from application to award

## User Permissions

### Permission Levels
- **Millennium Admin**: Full access to all applications
- **User Admin**: Access to client-specific applications
- **Client Users**: Access to their organization's applications
- **Assignees**: Access to assigned applications

### Action Permissions
- **Create Applications**: Permission to create new applications
- **Edit Applications**: Permission to modify applications
- **Delete Applications**: Permission to remove applications
- **Change Status**: Permission to change application status
- **Assign Users**: Permission to assign applications to users

## Technical Features

### Data Validation
- **Required Fields**: Enforce mandatory information
- **Format Validation**: Ensure proper data formats
- **Business Rules**: Enforce application business logic
- **Duplicate Prevention**: Prevent duplicate applications

### Performance Features
- **Pagination**: Handle large application lists efficiently
- **Lazy Loading**: Load data as needed
- **Caching**: Improve response times
- **Search Optimization**: Fast search across large datasets

### Export Capabilities
- **CSV Export**: Application data for analysis
- **Excel Export**: Formatted application reports
- **PDF Export**: Print-ready application summaries
- **Bulk Export**: Export multiple applications simultaneously

### Mobile Responsiveness
- **Mobile-optimized Forms**: Touch-friendly application editing
- **Responsive Tables**: Adaptive application list display
- **Mobile Navigation**: Optimized for mobile devices
- **Offline Capability**: Basic offline application viewing

### Implementation Details
1. **User Choice Implementation:**
   - When an application status is changed to "Awarded", a confirmation dialog appears
   - If the user selects "Yes", `setGenerateAward(true)` is called
   - If the user selects "No", `setGenerateAward(false)` is called
   - The `generateAward` flag is passed to the `createAwardFromApplication` function
   - The function only creates an award if `generateAward` is true

2. **Logging:**
   - User choices are logged for tracking purposes
   - Logs include the application ID and the current user
