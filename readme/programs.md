# Programs Module

## Overview
The Programs module allows administrators to create, edit, and manage funding programs that clients can apply for. Programs can have monetary or non-monetary values.

## Components

### Money Input Components
The application uses multiple money input components that allow toggling between monetary values (numbers) and non-monetary descriptions (strings):

1. `MoneyInput.tsx` - Original component used in older parts of the application
2. `MoneyInputNew.tsx` - Newer version used in most program and application forms
3. `CurrencyInputField.tsx` - MUI-based component used in the Awards module

## Program Visibility Rules

### Full Clients
Full clients can only see programs for which they have an application. This means:
- If a program has been sent to a full client (i.e., an application exists), they can see it
- If no application exists for that program, it will not appear in their program list

### Flex Clients
Flex clients see programs based on matching attributes, regardless of applications. For a program to be visible to a flex client, ALL of the following conditions must be met:

1. The program must have "Show for Flex Clients" enabled
2. The program's organization types must include the client's organization type
   - Example: If client is a "Municipality", the program must include "Municipality" in its organization types
3. The program's states must either:
   - Include the client's state (e.g., "New Jersey"), OR
   - Include "ALL" in its states list
4. The program's counties must either:
   - Include the client's county (e.g., "Morris County"), OR
   - Include "ALL" in its counties list

This attribute-based filtering ensures flex clients only see relevant programs that match their profile, without requiring individual applications to be created.

## Known Caveats

### Money Input Toggle Behavior
When working with the money input components:

- The toggle button switches between numeric (monetary) and text (non-monetary) input modes
- Both values are preserved when toggling between modes
- Example use cases:
  - Monetary value: "$50,000" (stored as a number)
  - Non-monetary value: "50 footballs for schoolboard" (stored as a string)

### Future Improvements
- Consolidate the multiple money input components into a single, consistent implementation
- Standardize on either the React Bootstrap or Material UI approach
