# Awards Module

## Overview
The Awards module is a comprehensive system for managing grant awards throughout their entire lifecycle. It provides multiple views for different aspects of award management and includes detailed award tracking, budget management, reporting, and user role assignments.

## Main Views

### 1. Summary View
- **Route**: `/dashboard/awards/summary`
- **Purpose**: Provides a high-level overview of all awards
- **Features**:
  - Comprehensive awards table with key information
  - "Only Me" filter to show user-specific awards
  - Create new award functionality
  - Advanced filtering capabilities
  - Export and bulk operations

### 2. Finance View
- **Route**: `/dashboard/awards/finance`
- **Purpose**: Focuses on financial aspects of awards
- **Features**:
  - Financial data table with budget and payment information
  - Payment tracking and status monitoring
  - Financial reporting capabilities
  - Budget analysis tools

### 3. Project View
- **Route**: `/dashboard/awards/projects`
- **Purpose**: Organizes awards by projects
- **Features**:
  - Project-based award grouping
  - Project details and management
  - Award assignment to projects
  - Funding sources tracking
  - Project funding summaries

### 4. User View
- **Route**: `/dashboard/awards/userView`
- **Purpose**: User-centric view of award assignments
- **Features**:
  - User-specific award listings
  - Role-based award access
  - User performance metrics
  - Assignment management
- **Access**: Limited to millennium admins and user admins

## Awards Details Management

### Award Details Page
- **Route**: `/dashboard/award/:awardId/details`
- **Features**:
  - Complete award information editing
  - Status timeline tracking
  - Document management
  - Payment tracking
  - Notes and communication

### Budget Management
- **Route**: `/dashboard/award/:awardId/budget`
- **Features**:
  - Budget entry creation and editing
  - Budget version control
  - Line-item budget tracking
  - Budget approval workflows

### Role Assignment
- **Route**: `/dashboard/award/:awardId/roles`
- **Features**:
  - User role assignments for awards
  - Permission management
  - Role-based access control
  - Team collaboration setup

### Reporting Management
- **Route**: `/dashboard/award/:awardId/reporting`
- **Features**:
  - Report creation and management
  - Report scheduling and reminders
  - Compliance tracking
  - Report submission workflows

## Key Components

### Award Creation
- **New Award Route**: `/dashboard/award/new/details`
- **Process**:
  1. Award details input
  2. Budget setup
  3. Role assignments
  4. Initial documentation
  5. Status activation

### Status Management
- **Status Timeline**: Visual representation of award progress
- **Status Types**: Draft, Active, Under Review, Approved, Rejected, Closed
- **Automated Workflows**: Status-based notifications and actions

### Document Management
- **File Upload**: Support for various document types
- **Version Control**: Track document revisions
- **Access Control**: Role-based document access
- **Bulk Operations**: Download multiple files as ZIP

### Payment Tracking
- **Payment Entries**: Record and track payments
- **Payment Status**: Pending, Processed, Completed
- **Payment Reminders**: Automated notification system
- **Financial Reporting**: Payment summaries and analytics

## Filtering and Search

### Advanced Filters
- **Client Filter**: Filter by client organization
- **Assignee Filter**: Filter by assigned users
- **Status Filter**: Filter by award status
- **Date Filters**: Filter by various date ranges
- **Custom Filters**: User-defined filter combinations

### Search Capabilities
- **Text Search**: Search across award fields
- **Quick Filters**: Predefined filter sets
- **Saved Filters**: Save and reuse filter combinations

## User Permissions

### Millennium Admin
- Full access to all awards and features
- Can create, edit, and delete awards
- Access to all views and management functions

### Millennium Manager
- Broad access to award management
- Can edit award preferences and settings
- Limited administrative functions

### User Admin
- Client-specific award management
- User and role management within their scope
- Access to user view

### Regular Users
- Access to assigned awards only
- Limited editing capabilities
- View-only access to most features

## Integration Points

### Applications Module
- Automatic award creation from approved applications
- Application-to-award data transfer
- Status synchronization

### Clients Module
- Client-specific award filtering
- Client preference integration
- Client-based access control

### Users Module
- User role assignments
- Permission management
- User-specific award access

### Notifications Module
- Award status change notifications
- Payment reminders
- Report due notifications
- Compliance alerts

## Technical Features

### Real-time Updates
- Live status updates
- Collaborative editing support
- Automatic data refresh

### Export Capabilities
- CSV export for data analysis
- Excel export with formatting
- PDF report generation
- Bulk document downloads

### Mobile Responsiveness
- Optimized for mobile devices
- Touch-friendly interfaces
- Responsive table layouts
- Mobile-specific navigation
