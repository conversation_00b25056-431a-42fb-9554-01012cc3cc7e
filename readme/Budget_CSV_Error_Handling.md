# Enhanced CSV Error Handling for Budget Component

## Overview

The Budget component's CSV import functionality has been enhanced to provide detailed, actionable error messages when CSV parsing fails. Instead of generic error messages, users now receive specific information about what went wrong and where.

## Features

### 1. Line-Specific Error Reporting
- **Line Numbers**: Each error message includes the exact line number where the issue occurred
- **Line Preview**: Shows the actual content of the problematic line (truncated if too long)
- **Context**: Helps users quickly locate and fix issues in their CSV files

### 2. Field-Specific Validation
- **Missing Required Fields**: Identifies when required columns like 'Name' are empty
- **Invalid Numbers**: Detects non-numeric values in numeric fields
- **Negative Values**: Flags negative amounts in budget fields
- **Calculation Errors**: Validates that budget calculations are consistent

### 3. Relationship Validation
- **Parent-Child References**: Ensures child items reference valid parent IDs
- **Data Integrity**: Validates the structure and relationships within the CSV data

## Error Message Examples

### Before Enhancement
```
Failed to parse CSV file. Please check the file format.
```

### After Enhancement
```
CSV parsing failed at line 15: '<PERSON>,<PERSON><PERSON>,invalid-number,Manager'. The 'budget_amount' field contains 'invalid-number' which is not a valid number.

CSV parsing failed at line 23: 'Jane,Smith,,Director'. The required 'Name' field is missing or empty.

CSV parsing failed at line 31: '4,,Travel,15000,8000,10000,1500,800,700'. Budget calculation error for "Travel": Total Budget (15000) does not equal Award Expended (8000) + Award Balance (10000). Please verify the calculations.
```

## Implementation Details

### Enhanced CSV Parser
- **Line Tracking**: Each parsed row includes metadata about its line number and raw content
- **Error Context**: Preserves original line content for error reporting
- **Graceful Handling**: Continues parsing valid rows while collecting all errors

### Validation Function
The `validateCsvData` function now performs comprehensive validation:

```typescript
const validateCsvData = (csvData: CsvData[]): { valid: boolean; errors: string[] } => {
  // Enhanced validation with detailed error messages
  // - Required field validation
  // - Numeric field validation
  // - Calculation consistency checks
  // - Parent-child relationship validation
}
```

### Error Types Detected

1. **Missing Required Fields**
   - Empty or missing 'Name' field
   - Missing required columns in header

2. **Invalid Numeric Values**
   - Non-numeric strings in numeric fields
   - Negative values where not allowed
   - Invalid number formats

3. **Calculation Inconsistencies**
   - Award Budget ≠ Award Expended + Award Balance
   - Match Amount ≠ Match Expended + Match Balance

4. **Relationship Errors**
   - Child items referencing non-existent parent IDs
   - Invalid parent-child hierarchies

## Usage

The enhanced error handling is automatically active when importing CSV files through the Budget component. Users will see detailed error messages in toast notifications, allowing them to:

1. **Identify Issues Quickly**: Line numbers and previews help locate problems
2. **Understand Problems**: Field-specific messages explain what's wrong
3. **Fix Issues Efficiently**: Actionable guidance on how to resolve each error

## Benefits

- **Improved User Experience**: Clear, actionable error messages
- **Faster Problem Resolution**: Specific line and field information
- **Better Data Quality**: Comprehensive validation catches more issues
- **Reduced Support Burden**: Users can self-diagnose and fix CSV issues

## Technical Notes

- Error messages are displayed using the existing `tossError` toast system
- Line numbers are 1-based to match typical text editor line numbering
- Long lines are truncated in error messages to maintain readability
- All validation occurs before any data processing to prevent partial imports
