# Award Table Sorting - Full Dataset Fix

## Problem Description

The award table sorting for **users/assignees** and **clients** columns was not working properly for the full dataset. The sorting was only working on the current page rather than sorting the entire dataset across all pages.

### Root Causes

1. **Sequelize Distinct + Association Sorting Issues**: When using `distinct: true` with `order` on associated tables, <PERSON><PERSON><PERSON> can generate problematic SQL queries
2. **Subquery Problems**: The default Sequelize behavior with associations and pagination creates subqueries that don't handle sorting correctly
3. **NULL Handling**: Inconsistent handling of NULL values in associated tables (assignees can be NULL, but clients should not be)
4. **Join Requirements**: Inconsistent `required` settings between different associations

## Solution Implemented

### 1. Fixed Service Layer (`src/services/award.service.ts`)

**Changes Made**:
- Added `col: 'awards.id'` to specify the column for distinct counting
- Added `subQuery: false` to disable problematic subquery generation
- Made assignee join consistently `required: false` in all functions
- Made program join consistently `required: false` in all functions

**Before**:
```typescript
const awardAttributes: any = {
  // ... other config
  distinct: true,
  // col: 'awards.id', // commented out
  // subQuery: false, // commented out
};
```

**After**:
```typescript
const awardAttributes: any = {
  // ... other config
  distinct: true,
  col: 'awards.id', // Specify the column to apply distinct on for proper counting
  subQuery: false, // Disable subquery to fix association sorting issues
};
```

### 2. Enhanced Sorting Logic (`src/helpers/award.helper.ts`)

**Changes Made**:
- Implemented proper NULL handling using SQL CASE statements
- Used Sequelize `literal()` for complex sorting expressions
- Added `NULLS LAST` clause to ensure consistent NULL handling
- Created multi-level sorting for associations with potential NULL values

**Before**:
```typescript
case 'assignee':
  sortingQuery = [['assignee', 'name', sortOrder]];
  break;
case 'client':
  sortingQuery = [['client', 'name', sortOrder]];
  break;
```

**After**:
```typescript
case 'assignee':
  // Handle NULL assignees properly - put them at the end regardless of sort order
  sortingQuery = [
    [literal(`CASE WHEN "assignee"."name" IS NULL THEN 1 ELSE 0 END`), 'ASC'],
    [literal(`"assignee"."name" ${sortOrder} NULLS LAST`)]
  ];
  break;
case 'client':
  // Client should always have a value due to required: true, but handle NULLs just in case
  sortingQuery = [
    [literal(`"client"."name" ${sortOrder} NULLS LAST`)]
  ];
  break;
```

## Technical Details

### Sequelize Configuration Changes

1. **`distinct: true` with `col: 'Award.id'`**: Ensures proper counting when using JOINs
2. **`subQuery: false`**: Forces Sequelize to generate a single query instead of nested subqueries
3. **Consistent `required` settings**: Prevents inconsistent JOIN behavior

### SQL Generation Improvements

The new sorting logic generates SQL like:
```sql
ORDER BY 
  CASE WHEN "assignee"."name" IS NULL THEN 1 ELSE 0 END ASC,
  "assignee"."name" ASC NULLS LAST
```

This ensures:
- NULL assignees are always sorted to the end
- Non-NULL values are sorted alphabetically
- Consistent behavior across different sort orders

## Testing the Fix

### 1. Test Assignee Sorting

1. Navigate to any award table (Summary, Finance, User views)
2. Click on the "Assigned To" column header
3. Verify that:
   - Awards with assigned users are sorted alphabetically by user name
   - Awards without assignees appear at the end of the list
   - Sorting works across ALL pages, not just the current page
4. Click the column header again to reverse sort order
5. Verify the same behavior with reversed alphabetical order

### 2. Test Client Sorting

1. Navigate to award tables (Summary, Finance views - visible to Millennium users)
2. Click on the "Client" column header
3. Verify that:
   - Awards are sorted alphabetically by client name
   - Sorting works across ALL pages, not just the current page
4. Click the column header again to reverse sort order
5. Verify the same behavior with reversed alphabetical order

### 3. Test Pagination Consistency

1. Sort by assignee or client
2. Navigate through multiple pages
3. Verify that the sorting order is maintained across all pages
4. Check that the first item on page 2 would logically come after the last item on page 1

### 4. Test Mixed Data Scenarios

1. Ensure your test data includes:
   - Awards with assignees and awards without assignees
   - Awards from different clients
   - Various combinations of assigned/unassigned awards
2. Test sorting behavior with this mixed data

## Files Modified

1. **`src/services/award.service.ts`**:
   - `list()` function - main award listing
   - `getAwardsByUserId()` function - user-specific award listing

2. **`src/helpers/award.helper.ts`**:
   - `getFilterCriteria()` function - sorting logic

## Expected Behavior After Fix

✅ **Full Dataset Sorting**: Sorting now works across the entire dataset, not just the current page
✅ **Consistent NULL Handling**: NULL assignees are consistently placed at the end
✅ **Performance**: Optimized SQL queries with proper JOIN handling
✅ **Cross-Page Consistency**: Sort order is maintained when navigating between pages
✅ **Multiple Sort Support**: Complex sorting with proper precedence for NULL values

## Rollback Instructions

If issues arise, you can rollback by:

1. Reverting the `subQuery: false` and `col: 'Award.id'` changes in `award.service.ts`
2. Reverting the sorting logic in `award.helper.ts` to the simple association array format
3. The previous implementation will restore page-only sorting behavior

## Future Improvements

Consider implementing:
1. **Frontend Loading States**: Show loading indicators during sort operations
2. **Sort Direction Indicators**: Clearer visual indicators for current sort direction
3. **Multi-Column Sorting**: Allow sorting by multiple columns simultaneously
4. **Sort Persistence**: Remember user's preferred sort settings
