# Table Sorting and Pagination Fixes

## Problem Summary
The admin panel had issues with table sorting functionality where:
1. Sorting appeared to only apply to the currently displayed page rather than the entire dataset
2. Inconsistent behavior across different columns (some worked intermittently, others were restricted to visible page data)
3. Pagination didn't reset properly when sorting changed
4. **Client column sorting in awards tables only worked on the current page, not the entire dataset**

## Root Cause Analysis
The issues were caused by:
1. **Conflicting BootstrapTable Props**: Tables had both `defaultSorted` and `sort` props, which caused conflicts in remote sorting mode
2. **Inefficient State Management**: Some `onTableChange` handlers didn't check if sort parameters actually changed, causing unnecessary re-renders
3. **Inconsistent Implementation**: Different tables used different patterns for handling sorting and pagination
4. **Client-side JavaScript Sorting Fallback**: Awards tables had client-side sorting for the client column that only sorted the current page instead of proper server-side sorting

## Files Modified

### 1. src/helpers/award.helper.ts
**Changes Made**:
- Fixed server-side sorting for client column by using proper Sequelize association syntax
- Changed from SQL literal to Sequelize association array format for better compatibility
- This fixes the issue where client sorting only worked on the current page

**Before**:
```typescript
case 'client':
  // sortingQuery = [literal(`"client"."name" ${sortOrder}`)];
  break;
```

**After**:
```typescript
case 'client':
  sortingQuery = [['client', 'name', sortOrder]];
  break;
```

### 2. src/services/award.service.ts
**Changes Made**:
- Removed client-side JavaScript sorting fallback that only sorted the current page
- Now relies on proper server-side sorting for the entire dataset

**Before**:
```typescript
const data = await Award.scope('defaultScope').findAndCountAll(awardAttributes);

// If sorting by client, perform JavaScript sorting after fetching results.
if (query.sortBy === 'client') {
  data.rows = data.rows.sort(
    (a: { client: { name: string } }, b: { client: { name: string } }) => {
      const aName = a.client?.name || '';
      const bName = b.client?.name || '';
      return query?.sortOrder === 'asc'
        ? aName.localeCompare(bName)
        : bName.localeCompare(aName);
    }
  );
}

return data;
```

**After**:
```typescript
const data = await Award.scope('defaultScope').findAndCountAll(awardAttributes);

return data;
```

### 3. src/components/programs/ProgramsList.tsx
**Changes Made**:
- Removed conflicting `defaultSorted` prop from BootstrapTable configuration
- Improved `onTableChange` handler to only update when sort parameters actually change
- Added proper pagination reset when sorting changes
- Maintained `sort` prop for proper remote sorting

**Before**:
```typescript
<BootstrapTable
  defaultSorted={[{
    dataField: programFilters?.sortBy || 'startDate',
    order: (programFilters?.sortOrder || 'asc') as 'asc' | 'desc',
  }]}
  sort={{
    dataField: programFilters?.sortBy || 'startDate',
    order: (programFilters?.sortOrder || 'asc') as 'asc' | 'desc',
  }}
  // ... other props
/>
```

**After**:
```typescript
<BootstrapTable
  sort={{
    dataField: programFilters?.sortBy || 'startDate',
    order: (programFilters?.sortOrder || 'asc') as 'asc' | 'desc',
  }}
  // ... other props
/>
```

### 2. src/components/applications/ApplicationsList.tsx
**Changes Made**:
- Removed conflicting `defaultSorted` prop from BootstrapTable configuration
- Improved `onTableChange` handler to check for actual sort changes using correct property names
- Added proper pagination reset when sorting changes

**Before**:
```typescript
const onTableChange = (type, newState) => {
  if (type === 'sort') {
    const { sortField, sortOrder } = newState;
    const { sortByFiltered, sortOrderFiltered } = applicationFilters;
    // ... rest of handler
  }
};
```

**After**:
```typescript
const onTableChange = (type, newState) => {
  if (type === 'sort') {
    const { sortField, sortOrder } = newState;
    const { sortBy: currentSortBy, sortOrder: currentSortOrder } = applicationFilters;
    
    // Only update if the sort has actually changed
    if (currentSortOrder !== sortOrder || currentSortBy !== sortField) {
      // ... update logic
    }
  }
};
```

### 3. src/components/awards/FinanceView/FinanceAwardsTable.tsx
**Changes Made**:
- Removed conflicting `defaultSorted` prop from BootstrapTable configuration
- Maintained `sort` prop for proper remote sorting

**Before**:
```typescript
<BootstrapTable
  defaultSorted={[
    { dataField: searchParams.sortBy, order: searchParams.sortOrder as 'asc' | 'desc' },
  ]}
  sort={{ dataField: searchParams.sortBy, order: searchParams.sortOrder as 'asc' | 'desc' }}
  // ... other props
/>
```

**After**:
```typescript
<BootstrapTable
  sort={{ dataField: searchParams.sortBy, order: searchParams.sortOrder as 'asc' | 'desc' }}
  // ... other props
/>
```

## Technical Details

### BootstrapTable Remote Sorting Configuration
All tables now use consistent configuration:
```typescript
remote={{
  filter: true,
  pagination: true,
  sort: true,
}}
sort={{
  dataField: sortBy,
  order: sortOrder as 'asc' | 'desc',
}}
```

### State Management Pattern
Improved `onTableChange` handlers follow this pattern:
```typescript
const onTableChange = (type, newState) => {
  if (type === 'sort') {
    const { sortField, sortOrder } = newState;
    
    // Only update if sort has actually changed
    if (currentSortBy !== sortField || currentSortOrder !== sortOrder) {
      // Reset pagination
      if (tableRef.current) {
        tableRef.current.paginationContext.currPage = 1;
      }
      setPage(1);
      
      // Update sort parameters
      setFilters({ ...filters, sortBy: sortField, sortOrder });
    }
  }
};
```

## Impact
- **Fixed**: Sorting now works on the entire dataset, not just the current page
- **Fixed**: Pagination properly resets to page 1 when sorting changes
- **Fixed**: All columns have consistent sorting behavior
- **Improved**: Reduced unnecessary re-renders and API calls
- **Improved**: Consistent implementation across all tables

## Testing
See `readme/testing/table-sorting-validation.md` for comprehensive test plan.

## Notes
- Awards tables (UsersTable, ProjectsTable, SummaryAwardsTable) were already using the correct pattern with URL search params
- The fixes maintain backward compatibility
- Server-side sorting implementation was already correct; the issues were in the frontend table configuration
