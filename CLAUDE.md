# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Millennium Strategies Admin Panel, a React-based grant management application that helps organizations manage grant applications, awards, clients, users, and reporting. The system serves multiple user types including Millennium administrators, client administrators, and various analyst roles.

## Common Development Commands

### Development Server
```bash
npm run dev                    # Start development server with development environment
npm start                     # Start development server (default environment)
npm run start:development     # Start development server with development environment file
```

### Building
```bash
npm run build                 # Build for production
npm run build:staging         # Build for staging environment
npm run build:mock           # Build for mock environment (ESLint disabled)
npm run build:production     # Build for production environment (ESLint disabled)
```

### Testing & Code Quality
```bash
npm test                      # Run Jest tests
npm run format               # Format code with Prettier
npm run stylelint            # Lint and fix SCSS files
```

Note: ESLint runs automatically via lint-staged pre-commit hooks. There are no separate lint commands - linting occurs during the build process and commit hooks.

## Architecture Overview

### Frontend Stack
- **React 18** with TypeScript for type safety
- **Material-UI (MUI)** for consistent design components
- **React Router v6** for client-side routing
- **Axios** with custom API service singleton for HTTP requests
- **SCSS** with modular stylesheets for styling
- **Formik + Yup** for form handling and validation

### State Management & Context
- **React Context** for global state (UserSessionContext, StringsContext)
- **Custom Hooks** for reusable logic (useLocalStorage, useCurrentAward, etc.)
- **Context Providers** for feature-specific state (AwardContext, ProgramContext, UserDetailsContext)

### Key Architectural Patterns

#### Service Layer Pattern
All API interactions go through dedicated service files in `src/services/`:
- `apiService.ts` - Singleton HTTP client with request deduplication
- Feature-specific services (e.g., `userService.ts`, `programService.ts`)
- Centralized error handling and logging

#### Component Organization
- **Pages** (`src/pages/`) - Route-level components
- **Components** (`src/components/`) - Feature-organized reusable components
- **Shared** (`src/shared/`) - Common UI components and utilities
- **Layout** components for consistent page structure

#### Authentication & Authorization
- JWT token-based authentication stored in localStorage
- Role-based access control with user types:
  - `millenniumAdmin` - Full system access
  - `millenniumAnalyst` - Application management focus
  - `userAdmin` - Client-level administration
  - `userAnalyst` - Limited client user access
- Protected routes with role-based redirects

#### Data Flow Architecture
1. **Applications → Awards** - Approved applications can create awards
2. **Clients → Users** - Users are assigned to specific clients
3. **Programs → Applications** - Programs define available funding opportunities
4. **Awards → Budget/Payments/Reports** - Awards track financial and reporting data

### File Organization Strategy

#### Path Aliases (tsconfig.json)
The project uses TypeScript path mapping for cleaner imports:
```typescript
import { Component } from 'components/feature/Component';
import { apiService } from 'services/apiService';
import { UserType } from 'types/user';
```

#### Asset Management
- **SCSS**: Organized by feature with global theme variables
- **Icons**: Multiple icon libraries (Feather, FontAwesome, Material Design)
- **Images**: Centralized in `src/assets/images/`

### Environment Configuration
- Multiple environment files (.env.development, .env.staging, .env.production)
- Environment-specific build commands
- API URL and feature flags controlled via environment variables

### Deployment Pipeline
The project uses Bitbucket Pipelines for CI/CD:
- **Build**: Node.js 20 with npm install and build
- **Deploy**: AWS S3 deployment with CloudFront cache invalidation
- **Testing**: Tests are currently commented out but Jest is configured

### Key Dependencies for Development
- **@mui/material** & **@mui/icons-material** - UI component library
- **react-router-dom** - Client-side routing
- **axios** - HTTP client
- **formik** + **yup** - Form handling and validation
- **react-bootstrap-table-next** - Data table components
- **date-fns** - Date manipulation utilities
- **lodash** - Utility functions

### Development Guidelines

#### TypeScript Usage
- Strict TypeScript configuration enabled
- Type definitions in `src/types/` for all major entities
- Custom type definitions in `custom.d.ts` for third-party libraries

#### Error Handling
- Centralized error logging via `services/logger`
- API error handling in `apiService.ts` with outputError utility
- React Error Boundaries for component-level error catching

#### Performance Considerations
- API request deduplication in ApiServiceSingleton
- React.memo and useMemo for expensive computations
- Lazy loading for route-level code splitting potential

### Common Development Patterns

#### Custom Hooks Pattern
Use custom hooks for stateful logic:
```typescript
const [storedValue, setValue, removeValue] = useLocalStorage<string>('key', 'version');
const currentUser = useUserSession();
```

#### Service Layer Usage
All API calls should go through service files:
```typescript
import { getPrograms } from 'services/programService';
import { updateClient } from 'services/clientService';
```

#### Context Provider Pattern
Wrap components that need shared state:
```typescript
<AwardContextWrapper>
  <AwardRelatedComponents />
</AwardContextWrapper>
```

This architecture supports a complex grant management workflow while maintaining separation of concerns and scalability for future feature development.