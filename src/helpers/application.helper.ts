import { WhereOptions } from 'sequelize';
import { Column } from 'exceljs';
import dayjs from 'dayjs';
import { camelCase, snakeCase } from 'lodash';
import <PERSON>zZip from 'pizzip';

import advancedFormat from 'dayjs/plugin/advancedFormat';

import { FilterApplications } from '../controllers/types/application.type';

const csv = require('fast-csv');
const ExcelJS = require('exceljs');
const { Op } = require('sequelize');
const strings = require('../config/strings');

dayjs.extend(advancedFormat);

export function reduceTZ(date?: Date | string, placeholder = 'TBD') {
  const dateObject = dayjs(date);

  return date !== undefined && dateObject.isValid()
    ? dateObject.format('dddd, MMMM Do YYYY')
    : placeholder;
}

export function exportDateFormat(date?: Date | string, placeholder = 'TBD') {
  if (!date) return placeholder;
  let value;

  if (typeof date === 'object')
    value = dayjs(date.toUTCString().split('Z')[0]).format('MM/DD/YYYY').toString();

  if (typeof date === 'string' && date.match(/^\d{4}/) !== null) {
    const modifiedDate = date.split('-');
    value = `${modifiedDate[1]}/${modifiedDate[2]}/${modifiedDate[0]}`;
  }
  return value || date;
}

export const getFilterCriteria = (
  {
    names,
    assignees,
    funders,
    status,
    dateFrom: startDate,
    dateTo: endDate,
    sortBy,
    sortOrder,
    endDateType: dateType = 'endsAt',
  }: FilterApplications,
  clientIds?: number[]
) => {
  const endDateType = dateType === 'Due Date' ? 'endsAt' : dateType;

  const NO_CLIENT_ASSIGNEE_ID = '-1';
  let sortingQuery =
    sortBy && sortOrder
      ? ``
      : `DATE(${snakeCase(endDateType)}) < CURRENT_DATE NULLS FIRST, ABS(DATE(${snakeCase(
          endDateType
        )}) - CURRENT_DATE) NULLS FIRST`;

  if (sortingQuery === ``) {
    switch (sortBy) {
      case 'assignee':
        sortingQuery = `"assigneeName"`;
        break;
      case 'client':
        sortingQuery = `"clientName"`;
        break;
      case 'funder':
        sortingQuery = 'funder';
        break;
      case 'programName':
        sortingQuery = 'name';
        break;
      case 'startDate':
        sortingQuery = '"startsAt"';
        break;
      default:
        sortingQuery = `"${snakeCase(endDateType)}"`;
        break;
    }

    switch (sortOrder) {
      case 'asc':
        sortingQuery = `${sortingQuery} ASC NULLS LAST`;
        break;
      case 'desc':
        sortingQuery = `${sortingQuery} DESC NULLS FIRST`;
        break;
      default:
        break;
    }
  }

  const queryFilters: WhereOptions = {};

  const filterConstruct = (list: string[]) => {
    let filterConstr: WhereOptions = { [Op.in]: list };
    const noIdInd = list.indexOf(NO_CLIENT_ASSIGNEE_ID);

    if (noIdInd >= 0) {
      list.splice(noIdInd, 1);

      filterConstr = {
        [Op.or]: {
          [Op.is]: null,
          [Op.or]: {
            [Op.in]: list,
          },
        },
      };
    }

    return filterConstr;
  };

  if (clientIds && clientIds.length > 0) queryFilters.clientId = { [Op.in]: clientIds };
  if (assignees) {
    // Filter out non-numeric values from assignees to ensure only valid IDs are used
    const assigneeIds = assignees.split(',')
      .map(id => id.trim())
      .filter(id => !isNaN(Number(id)) && id !== '');

    if (assigneeIds.length > 0) {
      queryFilters.assigneeId = filterConstruct(assigneeIds);
    }
  }

  if (funders) {
    const splitFunders = funders.split(/(?<!,),(?!,)/g).map((e) => e.replace(/,,/g, ',').trim());

    queryFilters.funder = { [Op.iLike]: `%${splitFunders[0]}%` };

    const ors: WhereOptions = [];
    // eslint-disable-next-line guard-for-in
    for (const index in splitFunders) {
      const funder = splitFunders[index];

      ors.push({ [Op.iLike]: `%${funder}%` });
    }

    queryFilters.funder[Op.or] = ors;
  }

  // debugger
  if (names) {
    const namesArray = names.split(/(?<!,),(?!,)/g).map((e) => e.replace(/,,/g, ','));
    const substringsArray = namesArray.map((name) => ({
      [Op.iLike]: `%${name}%`,
    }));

    queryFilters.name = {
      [Op.or]: {
        [Op.in]: namesArray,
        [Op.or]: [...substringsArray],
      },
    };
  }

  /*
    DEV-258
    Start date within date range selection for programs - due date not relevant
    Due date within date range selection for applications - start date not as relevant
  */
  if (startDate && endDate) {
    // If dateFrom and dateTo are the same, set the time to 00:00:00
    const dateFrom = startDate === endDate ? new Date(startDate).setHours(0, 0, 0, 0) : startDate;
    const dateTo = startDate === endDate ? new Date(endDate).setHours(23, 59, 59, 999) : endDate;

    queryFilters[Op.and] = [{ [endDateType]: { [Op.between]: [dateFrom, dateTo] } }];
  }
  if (startDate && !endDate) {
    const dateFrom = new Date(startDate).setHours(0, 0, 0, 0);
    const dateTo = new Date(startDate).setHours(23, 59, 59, 999);

    queryFilters[Op.and] = [{ [endDateType]: { [Op.between]: [dateFrom, dateTo] } }];
  }
  const {
    applications: {
      statuses: { clientNotified, inProgress, grantSubmitted, grantAwarded, grantNotAwarded },
    },
  } = strings;

  if (status && status === 'grantProcessed') {
    // include Grant Awarded, Grant Submitted, Grant Not Awarded.
    queryFilters.status = { [Op.in]: [grantSubmitted, grantAwarded, grantNotAwarded] };
  } else if (status === 'all') {
    queryFilters.status = { [Op.in]: Object.values(strings.applications.statuses) };
  } else if (status === 'openStatuses') {
    // Open Statuses
    // Includes: In Progress, Client Notified.
    queryFilters.status = { [Op.in]: [clientNotified, inProgress] };
  } else if (status === 'openStatusesLate') {
    // Open Status - Late
    // Only show 'late' results where the date is less than the current date and the status is 'in progress' or 'client notified'.
    queryFilters.status = { [Op.in]: [clientNotified, inProgress] };
    queryFilters[Op.and] = [{ [endDateType]: { [Op.lte]: dayjs().subtract(1, 'day').toDate() } }];
  } else if (status === 'openStatusesActive') {
    // Open Status - Active
    // Only show current or future results where the date is greater than the current date and the status is 'in progress' or 'client notified'.
    queryFilters.status = { [Op.in]: [clientNotified, inProgress] };
    queryFilters[Op.and] = [{ [endDateType]: { [Op.gte]: dayjs().subtract(1, 'day').toDate() } }];
  } else if (status) {
    const { statuses } = strings.applications;
    queryFilters.status = statuses[status];
  } else {
    queryFilters.status = { [Op.in]: [clientNotified, inProgress] };
  }

  return { queryFilters, sortingQuery, status };
};

export const createXlxsFile = async (
  csvData: any[],
  headers: string[],
  worksheetName: string = 'Worksheet'
): Promise<Buffer> => {
  const workbook = new ExcelJS.Workbook();

  workbook.creator = 'Millennium Strategies LLC';
  workbook.lastModifiedBy = 'Millenium Strategies LLC';
  workbook.created = new Date();
  workbook.modified = new Date();
  workbook.lastPrinted = new Date();

  const sheet = workbook.addWorksheet(worksheetName);

  sheet.addRows([headers].concat(csvData));

  sheet.columns.forEach((column: Column) => {
    let maxLength = 0;
    column.eachCell({ includeEmpty: true }, (cell) => {
      const columnLength = cell.value ? cell.value.toString().length : 10;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    // eslint-disable-next-line no-param-reassign
    column.width = maxLength < 10 ? 10 : maxLength + 3;
  });

  const resultFile = await workbook.xlsx.writeBuffer();
  return resultFile;
};

export const createCsvFile = async (
  csvData: any[],
  headers: string[],
  fileName: string = 'result.csv',
  options = {}
) => {
  const buf = await csv.writeToBuffer([headers].concat(csvData), {
    ...options,
    headers: true,
  });

  const zip = new PizZip();

  zip.file(fileName, buf);

  const compressedFile = zip.generate({ type: 'nodebuffer' });

  return compressedFile;
};

export default getFilterCriteria;
