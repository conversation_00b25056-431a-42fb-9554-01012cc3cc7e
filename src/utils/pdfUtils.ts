import { pdfjs } from 'react-pdf';

/**
 * Converts screen coordinates to PDF coordinates
 * @param x Screen X coordinate
 * @param y Screen Y coordinate
 * @param viewport PDF viewport
 * @returns PDF coordinates [x, y]
 */
export const screenToPdfCoordinates = (
  x: number,
  y: number,
  viewport: pdfjs.PDFPageViewport
): [number, number] => {
  // PDF coordinates start from bottom-left, screen coordinates from top-left
  // Use the transform method from the viewport for more accurate conversion
  const [pdfX, pdfY] = viewport.convertToPdfPoint(x, y);

  console.log('Screen coordinates:', { x, y });
  console.log('PDF coordinates:', { pdfX, pdfY });

  return [pdfX, pdfY];
};

/**
 * Checks if a point is inside a rectangle with a small tolerance
 * @param x Point X coordinate
 * @param y Point Y coordinate
 * @param rect Rectangle coordinates [x1, y1, x2, y2]
 * @param tolerance Tolerance in points (default: 5)
 * @returns Boolean indicating if point is inside rectangle
 */
export const isPointInRect = (
  x: number,
  y: number,
  rect: [number, number, number, number],
  tolerance: number = 5
): boolean => {
  const [x1, y1, x2, y2] = rect;

  // Add tolerance to make hit detection more forgiving
  const isInside =
    x >= x1 - tolerance && x <= x2 + tolerance && y >= y1 - tolerance && y <= y2 + tolerance;

  console.log('Checking point in rect:', { x, y, rect, isInside });

  return isInside;
};

/**
 * Extracts links from a PDF page at the specified coordinates
 * @param page PDF page object
 * @param x Screen X coordinate
 * @param y Screen Y coordinate
 * @param scale Current scale factor
 * @returns Array of link objects with url and title
 */
export const extractLinksFromPage = async (
  page: pdfjs.PDFPageProxy,
  x: number,
  y: number,
  scale: number = 1.0
): Promise<Array<{ url: string; title: string }>> => {
  try {
    // Get the viewport to convert coordinates
    const viewport = page.getViewport({ scale });

    // Get annotations (which include links)
    const annotations = await page.getAnnotations();

    console.log('PDF viewport:', {
      width: viewport.width,
      height: viewport.height,
      scale: viewport.scale,
      rotation: viewport.rotation,
    });

    // Convert screen coordinates to PDF coordinates
    const [pdfX, pdfY] = viewport.convertToPdfPoint(x, y);

    console.log('Screen coordinates:', { x, y });
    console.log('PDF coordinates:', { pdfX, pdfY });

    // Filter for link annotations
    const links = annotations
      .filter((annotation) => {
        // Check if it's a link annotation
        if (annotation.subtype !== 'Link') return false;

        // Log the link annotation for debugging
        console.log('Checking link annotation:', {
          rect: annotation.rect,
          url: annotation.url,
          title: annotation.title,
        });

        // Check if the point is inside the link rectangle with tolerance
        const isInside = isPointInRect(
          pdfX,
          pdfY,
          annotation.rect as [number, number, number, number],
          10 // Increased tolerance for better hit detection
        );

        console.log('Is point inside link rect:', isInside);

        return isInside;
      })
      .map((link) => {
        // Ensure URL is properly formatted
        let url = link.url || '';

        // If URL doesn't have a protocol, add https://
        if (url && !url.match(/^[a-z]+:\/\//i)) {
          url = `https://${url}`;
        }

        return {
          url,
          title: link.title || url || 'Link',
        };
      })
      .filter((link) => link.url); // Filter out links with empty URLs

    console.log('Detected PDF links:', links);
    return links;
  } catch (error) {
    console.error('Error extracting links from page:', error);
    return [];
  }
};

/**
 * Debug function to log all links in a PDF page
 * @param page PDF page object
 * @returns Array of all link objects in the page
 */
export const debugAllLinksInPage = async (
  page: pdfjs.PDFPageProxy
): Promise<Array<{ url: string; title: string; rect: number[] }>> => {
  try {
    // Get annotations (which include links)
    const annotations = await page.getAnnotations();

    // Filter for link annotations
    const links = annotations
      .filter((annotation) => annotation.subtype === 'Link')
      .map((link) => ({
        url: link.url || '',
        title: link.title || link.url || 'Link',
        rect: link.rect || [],
      }));

    console.log('All links in PDF page:', links);
    return links;
  } catch (error) {
    console.error('Error debugging links in page:', error);
    return [];
  }
};

/**
 * Gets all links from a PDF page
 * @param page PDF page object
 * @returns Array of link objects with url, title, and rect
 */
export const getAllLinksFromPage = async (
  page: pdfjs.PDFPageProxy
): Promise<Array<{ url: string; title: string; rect: number[] }>> => {
  try {
    // Get annotations (which include links)
    const annotations = await page.getAnnotations();

    // Filter for link annotations
    const links = annotations
      .filter((annotation) => annotation.subtype === 'Link')
      .map((link) => {
        // Ensure URL is properly formatted
        let url = link.url || '';

        // If URL doesn't have a protocol, add https://
        if (url && !url.match(/^[a-z]+:\/\//i)) {
          url = `https://${url}`;
        }

        // Extract domain for display
        let domain = '';
        try {
          domain = new URL(url).hostname;
        } catch (e) {
          domain = url;
        }

        return {
          url,
          title: link.title || domain || 'Link',
          rect: link.rect || [],
        };
      })
      .filter((link) => link.url); // Filter out links with empty URLs

    console.log('All links in PDF page:', links);
    return links;
  } catch (error) {
    console.error('Error getting all links from page:', error);
    return [];
  }
};
