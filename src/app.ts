import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';

import auth from './middleware/authorization';
import routes from './routes';
import morgan from './config/morgan';
import './cron/scheduler';

require('dotenv').config();

console.log(
  '🔍 ProgramFiles Model - AWS_CLOUDFRONT_FILE_URL:',
  process.env.AWS_CLOUDFRONT_FILE_URL
);

const app = express();

const { DB_HOST, DB_NAME, NODE_ENV } = process.env;

if (NODE_ENV !== 'test') {
  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);

  // To prevent our app from firing CORS error while addressing the API.
  app.use(
    cors({
      exposedHeaders: ['Date'],
    })
  );
}

const fileUpload = require('express-fileupload');

const { validateToken } = auth;

app.use(bodyParser.json());
app.use(fileUpload());
app.use(express.static('public'));

// Temporary middleware to log all requests
app.use((req, res, next) => {
  console.log('Incoming Request:', req.method, req.url);
  // console.log('Request Body:', req.body);
  next();
});

/*
    Authorization token checker middleware is excluded from the following endpoints:
    - Logging the user in.
    - Activation of a newly created user.
    - Restoring the user's password.
*/
app.use(
  validateToken.unless({
    path: [
      { url: '/sessions', methods: ['POST'] },
      { url: '/users/password', methods: ['POST', 'PATCH'] },
      { url: '/public/templates/client-memo-ls.docx', methods: ['GET'] },
      { url: '/public/templates/client-memo-pt.docx', methods: ['GET'] },
      { url: '/users/invite', methods: ['GET'] },
      { url: '/users/create-invited-user', methods: ['POST'] },
      { url: '/users', methods: ['POST'] },
      { url: '/strings', methods: ['GET'] },
    ],
  })
);

console.log(`Connection has been established successfully to ${DB_NAME} on '${DB_HOST}'.`);

app.use('/', routes);

export default app;
