import { Model, DataTypes, Op, WhereOptions, Sequelize } from 'sequelize';
import { sequelize as connection } from '../config/database';
import { parseHTMLTemplate, sendEmailLetter } from '../helpers/mail.helper';
import { generateSalt, hashPassword, comparePasswords } from '../helpers/password.helper';
import { UserType } from './types/user';
import strings from '../config/strings';

const jwt = require('jsonwebtoken');
const Login = require('./logins').default(connection);
const NotificationPreference = require('./notification-preferences').default(connection);

export default function (sequelize: Sequelize) {
  class Employees extends Model {
    public id!: number;

    public password!: string;

    public name!: string;

    public email!: string;

    public primaryClientId!: number;

    public clientCreatorId!: number;

    public userType!: UserType;

    public position!: string;

    public phone!: string;

    public location?: string;

    public lastLoggedIn?: Date;

    public isInvited?: boolean;

    public inviteAccepted?: boolean;

    public isVerified?: boolean;

    public defaultRole?: string;

    public enabled?: boolean;

    public createdAt?: Date;

    public updatedAt?: Date;

    static associate(models: any) {
      // Employees.belongsToMany(models.Client, {
      //   through: models.EmployeeClient,
      //   foreignKey: 'client_id',
      // });
      // Employees.belongsTo(models.Client, {
      //   foreignKey: 'client_creator_id',
      //   as: 'createdByClient',
      // });
      Employees.belongsToMany(models.Project, {
        through: models.ProjectAward,
        as: 'projects',
        foreignKey: 'user_id',
      });

      Employees.hasMany(models.AwardBudgetEntry, {
        foreignKey: 'user_id',
        as: 'awardBudgetEntries',
      });

      Employees.hasMany(models.Award, {
        foreignKey: 'assignee_id',
        as: 'assignedAwards',
      });

      Employees.hasMany(models.AwardFile, {
        foreignKey: 'user_id',
        as: 'awardFiles',
      });

      Employees.hasMany(models.AwardPayment, {
        foreignKey: 'user_id',
        as: 'awardPayments',
      });

      Employees.hasMany(models.AwardReport, {
        foreignKey: 'user_id',
        as: 'awardReports',
      });

      Employees.hasMany(models.AwardNotification, {
        foreignKey: 'user_id',
        as: 'awardNotifications',
      });

      Employees.hasMany(models.AwardUserRole, {
        foreignKey: 'user_id',
        as: 'userRoles',
      });

      Employees.hasMany(models.NotificationPreference, {
        foreignKey: 'user_id',
        as: 'notificationPreferences',
      });
    }

    checkPassword(password: string) {
      return comparePasswords(password, this.password);
    }

    async changePassword(password: string) {
      try {
        const { salt, hashedpassword } = hashPassword(password, generateSalt(10));
        await this.update({ password: salt + hashedpassword });

        return true;
      } catch (err) {
        console.error(err);
        return false;
      }
    }

    static async changePassword(token: string, password: string) {
      try {
        const { userId, pass } = jwt.verify(token, process.env.JWT_SECRET);
        const user = await Employees.findOne({ where: { id: userId, password: pass } });

        if (user === null) {
          throw new Error('This user does not exist.');
        } else {
          const { salt, hashedpassword } = hashPassword(password, generateSalt(10));
          await user.update({ password: salt + hashedpassword });

          return true;
        }
      } catch (error) {
        throw error;
      }
    }

    static async findByQuery(
      field: string,
      query: string,
      attributes: string[],
      isAwardSearch: boolean | string = true,
      clients: string = ''
    ) {
      const whereClause: WhereOptions = {};
      const queriedField = Employees.getAttributes()[field];
      const appOnlySearchFilter = [
        strings.users.userTypes.userAdmin,
        strings.users.userTypes.userAnalyst,
      ];

      if (!queriedField) {
        throw new Error(`The column \`${field}\` doesn't exist in table \`employees\`.`);
      } else if (queriedField.type instanceof DataTypes.STRING) {
        whereClause[field] = { [Op.iLike]: `%${query}%` };
      } else if (queriedField.type instanceof DataTypes.INTEGER) {
        const intQuery = parseInt(query, 10);

        if (Number.isNaN(intQuery)) {
          throw new Error(`Incompatible query type for column \`${field}\`.`);
        } else {
          whereClause[field] = { [Op.eq]: intQuery };
        }
      }

      if (clients.length > 0) {
        const clientIlikeClauses = clients
          .split(',')
          .map((client, i) => ` ${i > 0 ? 'AND' : ''} name ILIKE '%${client}%'`);

        const clientIds = await sequelize.literal(
          `(
            SELECT ID from clients WHERE ${clientIlikeClauses}
          )`
        );

        if (clientIds.toString().length > 0 && isAwardSearch) {
          whereClause.clientCreatorId = { [Op.in]: clientIds };
        }
      }

      if (isAwardSearch === false || isAwardSearch === 'false')
        whereClause.userType = { [Op.notIn]: appOnlySearchFilter };

      return Employees.findAll({ attributes, where: whereClause, order: [[field, 'ASC']] });
    }

    static async findByToken(token: string) {
      try {
        const tokenRecord = await Login.findOne({ where: { token } });

        if (tokenRecord === null) {
          throw new Error('The provided authorization token is not registered on the platform.');
        } else {
          try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const { userId } = decoded;

            try {
              const tokenUser = await Employees.findByPk(userId);

              if (tokenUser === null) {
                console.error('A user associated with this token does not exist.');
                throw new Error('A user associated with this token does not exist.');
              } else {
                const { password, ...otherDataValues } = tokenUser.dataValues;
                tokenUser.dataValues = otherDataValues;

                return tokenUser;
              }
            } catch (error) {
              console.error('Failed to retrieve the user associated with a token.');
              throw error;
            }
          } catch (error) {
            tokenRecord.destroy();
            console.error('Failed to verify the provided authorization token.');
            throw error;
          }
        }
      } catch (error) {
        throw error;
      }
    }

    async sendPasswordResetLink() {
      try {
        const token = jwt.sign({ userId: this.id, pass: this.password }, process.env.JWT_SECRET, {
          expiresIn: '1d',
        });

        const passwordResetLink = `${process.env.PUBLIC_URL}resetpassword/${token}`;

        const emailHtml = parseHTMLTemplate(
          `${__dirname}/../email_templates/forgot-password.html`,
          {
            username: this.name,
            passwordResetLink,
          }
        );

        try {
          await sendEmailLetter({
            from: process.env.SERVICE_MAILER_EMAIL,
            to: this.email,
            subject: 'Password Reset Request',
            plainver: `Reset password here: ${passwordResetLink}`,
            htmlver: emailHtml,
          });

          return true;
        } catch (err) {
          throw new Error('An error has occurred during the sending of a password reset email.');
        }
      } catch (err) {
        console.error(err);
        throw new Error('An error has occurred during the creation of a password reset token.');
      }
    }
  }

  Employees.init(
    {
      primaryClientId: {
        field: 'primary_client_id',
        type: DataTypes.INTEGER,
      },
      clientCreatorId: {
        field: 'client_creator_id',
        type: DataTypes.INTEGER,
      },
      userType: {
        type: DataTypes.ENUM,
        field: 'user_type',
        values: Object.values(strings.users.userTypes),
        defaultValue: strings.users.userTypes.millenniumManager,
        allowNull: false,
      },
      defaultRole: {
        type: DataTypes.ENUM,
        field: 'default_role',
        values: Object.values(strings.awards.defaultAwardRoles),
        defaultValue: null,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      position: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      password: {
        type: DataTypes.TEXT,
      },
      phone: {
        type: DataTypes.STRING,
      },
      location: {
        type: DataTypes.STRING,
        defaultValue: 'NY',
      },
      lastLoggedIn: {
        field: 'last_logged_in',
        type: DataTypes.DATE,
      },
      isInvited: {
        type: DataTypes.BOOLEAN,
        field: 'is_invited',
        defaultValue: false,
      },
      inviteAccepted: {
        field: 'invite_accepted',
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isVerified: {
        field: 'is_verified',
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      enabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      hooks: {
        beforeFind: async (options: any) => {
          const newOptions = options;

          const getAttachedClients = (category: string) => [
            sequelize.literal(
              `(
                SELECT json_agg(
                  json_build_object(
                    'id', c.id,
                    'name', c.name,
                    'clientType', c.client_type,
                    'billingType', c.billing_type,
                    'enabled', c.enabled,
                    'awardsEnabled', c.awards_enabled,
                    'canCreateAward', c.can_create_award,
                    'privateAwardsManagement', c.private_awards_management
                  )
                )
                FROM employee_clients
                JOIN clients c on employee_clients.client_id = c.id
                WHERE employee_clients.employee_id = employees.id AND category = '${category}'
              )`
            ),
            `${category}Clients`,
          ];

          newOptions.attributes = {
            include: [
              getAttachedClients('application'),
              getAttachedClients('award'),
              [
                Sequelize.literal(
                  `(
                    SELECT json_build_object(
                      'awardId', ur.award_id,
                      'userId', ur.user_id,
                      'role', ur.role,
                      'customUserRole', ur.custom_user_role
                    )
                    FROM award_user_roles ur
                    WHERE ur.user_id = employees.id
                    LIMIT 1
                  )`
                ),
                'userRole',
              ],
              [
                Sequelize.literal(
                  `(
                    SELECT json_agg(
                      json_build_object(
                        'id', c.id,
                        'name', c.name,
                        'clientType', c.client_type,
                        'billingType', c.billing_type,
                        'awardsEnabled', c.awards_enabled,
                        'canCreateAward', c.can_create_award,
                        'privateAwardsManagement', c.private_awards_management,
                        'enabled', c.enabled
                      )
                    )
                    FROM clients c
                    WHERE c.id = employees.client_creator_id
                  )`
                ),
                'clientCreator',
              ],
            ],
            ...(options.attributes || {}),
          };

          return newOptions;
        },
      },
      scopes: {
        read: {
          attributes: {
            exclude: ['password'],
          },
        },
        withAwards: {
          attributes: {
            include: [
              [
                sequelize.literal(`(
                  SELECT json_agg(
                    json_build_object(
                      'id', awards.id,
                      'stringId', awards.string_id,
                      'programId', awards.program_id,
                      'assigneeId', awards.assignee_id,
                      'clientId', awards.client_id,
                      'applicationId', awards.application_id,
                      'reportId', awards.report_id,
                      'status', awards.status,
                      'source', awards.source,
                      'category', awards.category,
                      'funder', awards.funder,
                      'startsOn', awards.starts_on,
                      'endsOn', awards.ends_on,
                      'department', awards.department,
                      'grantPurpose', awards.grant_purpose,
                      'grantProgramName', awards.grant_program_name,
                      'notes', awards.notes,
                      'dateApproved', awards.date_approved,
                      'dateLastRejected', awards.date_last_rejected,
                      'isApproved', awards.is_approved,
                      'awardAmount', awards.award_amount,
                      'awardExpended', awards.award_expended,
                      'awardBalance', awards.award_balance,
                      'matchAmount', awards.match_amount,
                      'matchExpended', awards.match_expended,
                      'matchBalance', awards.match_balance,
                      'paymentsRequested', awards.payments_requested,
                      'paymentsReceived', awards.payments_received,
                      'nextReportDueDate', r.due_date,
                      'clientName', c.name
                    )
                  )
                  FROM awards
                    LEFT JOIN award_reports r ON r.award_id = "awards"."id" AND r.due_date > NOW()
                    LEFT JOIN clients AS c ON c.id = awards.client_id
                  WHERE awards.assignee_id = employees.id
                )`),
                'awards',
              ],
            ],
          },
        },
        withAwardCounts: {
          attributes: {
            include: [
              [
                sequelize.literal(`(
                  SELECT COUNT(*)
                  FROM awards AS award
                  WHERE award.assignee_id = employees.id AND award.status = 'closed'
                )`),
                'closedAwardsCount',
              ],
              [
                sequelize.literal(`(
                  SELECT COUNT(*)
                  FROM Awards AS award
                  WHERE award.assignee_id = employees.id AND award.status != 'closed'
                )`),
                'openAwardsCount',
              ],
              [
                sequelize.literal(`(
                  SELECT COUNT(*)
                  FROM award_reports AS report
                  JOIN award_files AS file ON file.report_id = report.id
                  WHERE report.user_id = employees.id
                  AND (file."createdAt" < report.due_date OR report.due_date > NOW())
                )`),
                'totalReportsOnTime',
              ],
              [
                sequelize.literal(`(
                  SELECT COUNT(*)
                  FROM award_reports AS report
                  JOIN award_files AS file ON file.report_id = report.id
                  WHERE report.user_id = employees.id
                  AND (file."createdAt" > report.due_date OR report.due_date < NOW())
                )`),
                'lateReportsCount',
              ],
            ],
          },
        },
      },
      sequelize,
      modelName: 'employees',
    }
  );

  Employees.addHook('afterCreate', (user) => {
    const userType = user.getDataValue('userType');
    const { userAdmin, userAnalyst } = strings.users.userTypes;
    if ([userAdmin, userAnalyst].includes(userType)) {
      NotificationPreference.bulkCreate(
        [
          {
            userId: user.getDataValue('id'),
            preference: 'awardEmailNotification',
            settings: JSON.stringify({
              enabled: true,
            }),
          },
          {
            userId: user.getDataValue('id'),
            preference: 'awardBatchEmails',
            frequency: 'week',
            settings: JSON.stringify({
              time: '0900',
            }),
          },
          {
            userId: user.getDataValue('id'),
            preference: 'awardReminders',
            frequency: 'week',
            settings: JSON.stringify({
              time: '0900',
            }),
          },
        ],
        { validate: true }
      );
    }
  });

  Employees.addHook('beforeUpdate', async (user) => {
    const userType = user.getDataValue('userType');
    const { userAdmin, userAnalyst } = strings.users.userTypes;

    if ([userAdmin, userAnalyst].includes(userType)) {
      if (user.getDataValue('defaultRole') === 'primaryOrganizationalContact') {
        const clientUsers = await Employees.findAll({
          where: {
            clientCreatorId: user.getDataValue('clientCreatorId'),
            defaultRole: 'primaryOrganizationalContact',
          },
        });

        if (clientUsers.length >= 1) {
          throw new Error('Only one primary organizational contact is allowed per client.');
        }
      }
    }
  });

  return Employees;
}
