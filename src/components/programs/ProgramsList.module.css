.program-listing-wrapper {
  background: #ffffff;
  border: 1px solid #eff1f6;
  padding: 20px;
  border-radius: 12px;
  margin: 0px 16px;
  box-shadow: 0 0 15px 10px #f7f7f7;
}

.program-heading {
  font-family: Ralew<PERSON>;
  font-weight: 700;
  font-size: 20px;
  color: #323232;
}

.program-tagline {
  font-family: Ralew<PERSON>;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0%;
  color: #50535b;
}

.application-creation-btn {
  border-radius: 6px;
  background-color: #eff1f6 !important;
  color: #2c4474 !important;
  border: none !important;
}

.tooltip {
  background-color: #2c4474 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  padding: 8px 12px !important;
}

.unreadProgram {
  position: absolute;
  right: -5px;
  top: -5px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ff0000;
}

.actionButton {
  font-family: 'Raleway';
  font-weight: 600;
  font-size: 14px;
}

.applicationContentList {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.applicationContentListActive {
  opacity: 1;
}

.applicationContentListDisabled {
  opacity: 0.7;
}

.paginationWrapper {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  text-align: center;
}

.recordsShowingText {
  display: inline-block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #50535b;
}

.paginationControlsWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5rem;
}

.paginationFormField {
  display: inline-flex;
  align-items: center;
  margin: 0 0.5rem;
}

.labelText {
  margin-right: 0.5rem;
  font-size: 14px;
  color: #323232;
}

.pageInput {
  width: 50px;
  height: 32px;
  padding: 0.25rem 0.5rem;
  border: 1px solid #cfd6e4;
  border-radius: 4px;
  text-align: center;
}

.goButton {
  margin-left: 0.5rem;
  padding: 0.25rem 0.75rem;
  background-color: #2c4474;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.goButton:hover {
  background-color: #1f3665;
}

.tableWrapper {
  overflow-x: auto;
}

.noDataText {
  text-align: center;
  padding: 2rem;
  color: #50535b;
  font-size: 16px;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
