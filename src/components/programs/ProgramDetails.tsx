/* eslint-disable no-nested-ternary */

import { useState, useEffect, useCallback, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Form from 'react-bootstrap/Form';
import Button from 'react-bootstrap/Button';
import {
  KeyboardArrowDown,
  KeyboardArrowUp,
  ArrowBack,
  Share,
  ExpandMore,
  Close,
  OpenInFull,
  ZoomOutMap,
} from '@mui/icons-material';
import {
  Box,
  Stack,
  IconButton,
  Modal,
  Typography,
  CircularProgress,
  Alert,
  Menu,
  MenuItem,
} from '@mui/material';
import { Check, X as CloseIcon } from 'react-feather';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import dayjs from 'dayjs';
import { useLocation, useNavigate } from 'react-router';
import Dropzone, { IFileWithMeta, IUploadParams, StatusValue } from 'react-dropzone-uploader';
import logger from 'services/logger';
import './programs.css';
import { tossError, tossSuccess } from 'utils/toastTosser';
import { TextInput, FunderInput, MoneyInputNew, MultiSelectInput } from 'shared/inputs';
import DateField from 'components/applications/EditForm/DateField';
import reduceTZ from 'utils/dateUtils';
import CopyToClipboard from 'react-copy-to-clipboard';
import { CustomInput } from 'reactstrap';
import { useProgramContext } from 'hooks/ProgramContext';
import useUserSession from 'hooks/useUserSession';
import CustomFields from 'shared/CustomFields';
import {
  deleteprogramFile,
  markProgramsAsRead,
  updateProgram,
  getProgram,
} from 'services/programService';
import { Program } from 'types/program';
import api from 'services/apiService';
import useLocalStorage from 'hooks/useLocalStorage';
import ContactMillenniumModal from 'shared/ContactMillenniumModal';
import {
  ALL_COUNTIES_OPTION,
  ALL_STATES_OPTION,
  applicationsConfigs,
  getCountiesForStates,
  OrgTypes,
  states,
  statesWithAllOption,
} from 'constants/globalConstants';
import { useParams } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import DeleteProgram from './DeleteProgram';
import 'assets/scss/programDetailsnew.scss';
import { formatCurrencyValue } from 'utils/utilFunctions';
import LinkIcon from '@mui/icons-material/Link';
import { extractLinksFromPage } from 'utils/pdfUtils';

// Set up the worker from pdfjs-dist
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.mjs',
  import.meta.url
).toString();

const determineReadOnlyStatus = (user, program) => {
  // Check if user is millennium staff
  if (user.isMillenniumUser) {
    // Analysts are always read-only
    if (user.userType === 'millenniumAnalyst') {
      return true;
    }
    // Admin and Manager can edit
    return false;
  }

  // For client users (userAdmin, userAnalyst)
  // Check if the program is available to this client
  const isClientProgram = program?.showForFlexClient === true;

  // All client users are read-only for programs
  return true;
};

const getFallbackComponent = () => {
  return (
    <div className="error-container">
      <Alert severity="error">Something went wrong loading the program details.</Alert>
      <Button
        className="mt-3"
        onClick={() => (window.location.href = '/dashboard/programs')}
        variant="contained"
      >
        Return to Programs List
      </Button>
    </div>
  );
};

export default function ProgramDetailsWithErrorBoundary() {
  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => getFallbackComponent()}
      onReset={() => {
        // Reset the state of your app here
        window.location.href = '/dashboard/programs';
      }}
    >
      <ProgramDetails />
    </ErrorBoundary>
  );
}

function ProgramDetails() {
  const [form, setForm] = useState<Partial<Program>>({});
  const [isSectionVisible, setIsSectionVisible] = useState(true);
  const [errors, setErrors] = useState<Partial<Record<keyof Program, string>>>({});
  const [files, setFiles] = useState([]);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const { programDetailsProps, setProgramDetailsProps } = useProgramContext();
  const { program, readOnly, disabled, onSave, readList } = programDetailsProps || {};
  const [storedProgramDetails, storeProgramDetails] = useLocalStorage('programDetailsProps', 'v1');

  const currentUser = useUserSession();

  const [pdfError, setPdfError] = useState('');
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    links: Array<{ url: string; title: string }>;
  } | null>(null);
  const pdfContainerRef = useRef<HTMLDivElement>(null);

  const fetchPdfBlob = async (url: string) => {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/pdf',
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
        mode: 'cors',
        credentials: 'omit',
      });

      if (!response.ok) throw new Error(`Failed to fetch PDF (Status: ${response.status})`);

      const blob = await response.blob();
      const pdfBlobnew = new Blob([blob], { type: 'application/pdf' });
      setPdfBlob(pdfBlobnew);
    } catch (error) {
      console.error('Error fetching PDF:', error);
      setPdfError(error.message || 'Failed to load PDF file');
    }
  };

  const { id: programIdFromUrl } = useParams();

  useEffect(() => {
    if (programDetailsProps.program === null && storedProgramDetails) {
      setProgramDetailsProps(
        storedProgramDetails as {
          program: Program;
          onSave: () => void;
          readList: (userId: number) => Promise<void>;
          readOnly: boolean;
          disabled: boolean;
        }
      );
    }
  }, [programDetailsProps, storedProgramDetails, setProgramDetailsProps]);

  useEffect(() => {
    if (programDetailsProps?.program) {
      try {
        const getCategoryValues = (categoryData) => {
          if (!categoryData) return [];
          const categories = Array.isArray(categoryData) ? categoryData : [categoryData];
          return categories.map((cat) => {
            // Add null/undefined check
            if (cat === null || cat === undefined) return '';
            return typeof cat === 'number' && applicationsConfigs.categories[cat]
              ? applicationsConfigs.categories[cat]
              : cat;
          });
        };

        // Safely set form data with null checks
        setForm({
          name: program?.name || '',
          funder: program?.funder || '',
          fundingAmount: program?.fundingAmount || 0,
          varyingFundingAmount: program?.varyingFundingAmount || '',
          amountVaries: program?.amountVaries || false,
          source: Array.isArray(program?.source)
            ? program?.source
            : program?.source
              ? [program.source]
              : [],
          category: Array.isArray(program?.category)
            ? program.category.map((cat) =>
                typeof cat === 'number' ? applicationsConfigs.categories[cat] : cat
              )
            : program?.category
              ? [
                  typeof program.category === 'number'
                    ? applicationsConfigs.categories[program.category]
                    : program.category,
                ]
              : [],
          startsAt: program?.startsAt || null,
          endsAt: program?.endsAt || null,
          matchRequirements: program?.matchRequirements || '',
          performancePeriod: program?.performancePeriod || '',
          estimatedResponse: program?.estimatedResponse || '',
          customFields: program?.customFields || [],
          states: Array.isArray(program?.states)
            ? program.states
            : program?.states
              ? [program.states]
              : [],
          counties: Array.isArray(program?.counties)
            ? program.counties
            : program?.counties
              ? [program.counties]
              : [],
          orgTypes: Array.isArray(program?.orgTypes)
            ? program.orgTypes
            : program?.orgTypes
              ? [program.orgTypes]
              : [],
          showForFlexClient: program?.showForFlexClient ?? true,
        });
      } catch (error) {
        console.error('Error setting form data:', error);
        // Don't throw, just log the error
      }
    }
  }, [programDetailsProps]);

  const IsUserAdmin = currentUser.userType === 'millenniumAdmin';
  const IsClient = currentUser.applicationClients;
  const location = useLocation();
  const navigate = useNavigate();
  const isFromPrograms = true;
  const setField = (
    field: string,
    value: string | number | boolean | null | undefined | number[] | string[]
  ) => setForm((prevState) => ({ ...prevState, [field]: value }));

  const setAsRead = async () => {
    await markProgramsAsRead({ selected: [program], currentUser });

    if (readList) {
      await readList(currentUser.id);
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  const toggleSectionVisibility = () => {
    setIsSectionVisible((prev) => !prev);
  };
  const save = async () => {
    // eslint-disable-next-line no-alert
    if (window.confirm('Are you sure you want to edit this program?')) {
      const {
        name,
        funder,
        fundingAmount,
        varyingFundingAmount,
        amountVaries,
        source,
        category,
        startsAt,
        endsAt,
        matchRequirements,
        performancePeriod,
        estimatedResponse,
        customFields,
        states: selectedStates,
        counties: selectedCounties,
        orgTypes,
        showForFlexClient,
      } = form;

      const submittedFields = {
        id: program?.id,
        name,
        funder,
        fundingAmount: fundingAmount ?? 0.0,
        varyingFundingAmount,
        amountVaries: amountVaries ?? false,
        source: Array.isArray(source) ? source : source ? [source] : [],
        startsAt,
        endsAt,
        matchRequirements,
        performancePeriod,
        estimatedResponse,
        customFields: JSON.stringify(customFields ?? []),
        category: Array.isArray(category)
          ? category.map((cat) => applicationsConfigs.categories.indexOf(cat))
          : [],
        states: Array.isArray(selectedStates)
          ? selectedStates
          : selectedStates
            ? [selectedStates]
            : [],
        counties: Array.isArray(selectedCounties)
          ? selectedCounties
          : selectedCounties
            ? [selectedCounties]
            : [],
        orgTypes: Array.isArray(orgTypes) ? orgTypes : orgTypes ? [orgTypes] : [],
        showForFlexClient: showForFlexClient ?? true,
      };
      const result = await updateProgram(submittedFields);

      if (result) {
        logger.info('Updated a program.', { program: submittedFields });
        tossSuccess('The program was successfully edited.');
        if (onSave) onSave();
        const updatedProgramDetailsProps = {
          ...programDetailsProps,
          program: { ...program, ...submittedFields },
        };
        storeProgramDetails(updatedProgramDetailsProps);
        navigate(-1);
      } else {
        logger.error('Error updating a program.', { program: submittedFields });
        tossError('Error editing the program.');
      }
    }
  };

  let isMounted = true;

  const fetchFiles = async () => {
    if (!program?.id) return;

    try {
      const response = await api.get(`/programs/files/${program.id}`);
      if (isMounted) {
        setFiles(response.data);
      }
    } catch (error) {
      if (isMounted) {
        console.error('Error fetching files:', error);
        // Don't call tossError if component might be unmounted
      }
    }
  };

  // Separate the file fetching logic into its own useEffect with proper cleanup
  useEffect(() => {
    if (program?.id) {
      fetchFiles();
    }

    return () => {
      isMounted = false;
    };
  }, [program?.id]);

  const getUploadParams = (fileWithMeta: IFileWithMeta) => {
    if (!fileWithMeta.meta.previewUrl) {
      fileWithMeta.meta.previewUrl = URL.createObjectURL(fileWithMeta.file);
    }
    const body = new FormData();
    body.append('programId', program?.id.toString());
    body.append('file', fileWithMeta.file);
    body.append('size', fileWithMeta.meta.size.toString());
    body.append('name', fileWithMeta.meta.name);
    body.append('fileUrl', fileWithMeta.meta.previewUrl || '');
    body.append('type', fileWithMeta.meta.type);

    return {
      url: `${process.env.REACT_APP_API_URL}/programs/programfile`,
      body,
      headers: {
        Authorization: api.client?.defaults.headers.common.Authorization,
      },
    } as IUploadParams;
  };

  // Don't forget to clean up the URL object when the file is removed
  const handleChangeStatus = (fileWithMeta: IFileWithMeta, status: StatusValue) => {
    if (status === 'done') {
      fileWithMeta.remove();
      fetchFiles();
    }
  };

  useEffect(() => {
    if (program) {
      setField('startsAt', program?.startsAt);
      setField('endsAt', program?.endsAt?.toLocaleString());
    }
  }, [program]);

  useEffect(() => {
    const { name, funder, fundingAmount, amountVaries, source, category, startsAt, endsAt } = form;

    const errorList: Partial<Record<keyof Program, string>> & {
      undefineds?: string;
    } = {};

    if (typeof name !== 'undefined') {
      if (!name || name === '') errorList.name = 'This field is required.';
      else if (name.length < 3)
        errorList.name = 'Grant program names must have 3 or more characters.';
    }

    if (typeof startsAt !== 'undefined' && typeof endsAt !== 'undefined') {
      if (startsAt && endsAt) {
        if (dayjs(endsAt).isBefore(dayjs(startsAt))) {
          errorList.startsAt = 'Start Date must be earlier than Due Date.';
          errorList.endsAt = 'Due Date must be later than Start Date.';
        }
      }
    }

    if (Number.isNaN(fundingAmount) && !amountVaries)
      errorList.fundingAmount = 'Enter a valid funding amount.';

    if (
      [typeof name, typeof funder, typeof fundingAmount, typeof source, typeof category].includes(
        'undefined'
      )
    )
      errorList.undefineds = 'One or more required fields are undefined.';

    setErrors(errorList);
  }, [form]);

  const handleDeleteFile = async (fileId: number) => {
    try {
      const result = await deleteprogramFile(fileId);
      if (result) {
        tossSuccess('The file has been deleted successfully.');
        fetchFiles();
      } else {
        tossError('Failed to delete this file.');
      }
    } catch (error) {
      console.error(error);
    }
  };
  const handlediscard = () => {
    if (program) {
      setForm({
        name: program?.name,
        funder: program?.funder,
        fundingAmount: program?.fundingAmount,
        varyingFundingAmount: program?.varyingFundingAmount,
        amountVaries: program?.amountVaries,
        source: Array.isArray(program?.source)
          ? program?.source
          : program?.source
            ? [program.source]
            : [],
        category: Array.isArray(program?.category)
          ? program?.category
          : program?.category
            ? [program.category]
            : [],
        startsAt: program?.startsAt,
        endsAt: program?.endsAt,
        matchRequirements: program?.matchRequirements,
        performancePeriod: program?.performancePeriod,
        estimatedResponse: program?.estimatedResponse,
        customFields: program?.customFields,
      });
    }
  };
  const handleCopy = useCallback(() => {
    tossSuccess('Link copied to clipboard');
  }, []);

  const onDocumentLoadSuccess = ({ numPages: totalNumPages }) => {
    setNumPages(totalNumPages);
  };

  const handleExpandPdf = () => {
    setShowPdfModal(true);
  };

  const getCategoryDisplayValues = (
    categoryValues: string[] | string | number[] | number | undefined
  ) => {
    if (!categoryValues) return [];
    const values = Array.isArray(categoryValues) ? categoryValues : [categoryValues];
    return values
      .map((value) => {
        if (typeof value === 'number') {
          return applicationsConfigs.categories[value];
        }
        if (typeof value === 'string' && applicationsConfigs.categories.includes(value)) {
          return value;
        }
        return '';
      })
      .filter(Boolean);
  };

  const getSourceDisplayValues = (sourceIndices: number[] | number | undefined) => {
    if (!sourceIndices) return [];
    const indices = Array.isArray(sourceIndices) ? sourceIndices : [sourceIndices];
    return indices.map((index) => applicationsConfigs.sources[index]).filter(Boolean);
  };

  // Separate PDF blob fetching into its own useEffect with proper cleanup
  useEffect(() => {
    let isMounted = true;
    const abortController = new AbortController();

    const fetchPdfBlob = async (url: string) => {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            Accept: 'application/pdf',
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
          },
          mode: 'cors',
          credentials: 'omit',
          signal: abortController.signal,
        });

        if (!response.ok) throw new Error(`Failed to fetch PDF (Status: ${response.status})`);

        const blob = await response.blob();
        if (isMounted) {
          const pdfBlobnew = new Blob([blob], { type: 'application/pdf' });
          setPdfBlob(pdfBlobnew);
        }
      } catch (error) {
        // Only log and set error if not aborted and component is mounted
        if (error.name !== 'AbortError' && isMounted) {
          console.error('Error fetching PDF:', error);
          setPdfError(error.message || 'Failed to load PDF file');
        }
      }
    };

    if (files[0]?.pdfUrl) {
      fetchPdfBlob(files[0].pdfUrl);
    }

    return () => {
      isMounted = false;
      abortController.abort(); // Cancel any in-flight fetch
    };
  }, [files]);

  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    // Priority 1: URL parameter should always take precedence
    if (programIdFromUrl) {
      console.log('ProgramDetails: Fetching program data for ID from URL:', programIdFromUrl);
      const fetchProgramData = async () => {
        try {
          if (isMounted) {
            console.log('ProgramDetails: Setting loading state to true');
            setIsLoading(true);
          }
          console.log('ProgramDetails: Calling getProgram with ID:', Number(programIdFromUrl));
          const fetchedProgram = await getProgram(Number(programIdFromUrl));
          console.log('ProgramDetails: Result from getProgram:', fetchedProgram);

          // Only update state if component is still mounted
          if (isMounted && fetchedProgram) {
            console.log('ProgramDetails: Updating component with fetched program data');
            const updatedProps = {
              program: fetchedProgram,
              onSave,
              readList,
              readOnly: determineReadOnlyStatus(currentUser, fetchedProgram),
              disabled: false,
            };
            setProgramDetailsProps(updatedProps);
            storeProgramDetails(updatedProps);
            setIsLoading(false);
            setLoadError(null);
          } else if (isMounted && !fetchedProgram) {
            console.error('ProgramDetails: No program data returned for ID:', programIdFromUrl);
            setLoadError('Program not found or access denied');
            setIsLoading(false);
          }
        } catch (error) {
          console.error('ProgramDetails: Error fetching program:', error);
          if (isMounted) {
            setLoadError('Error loading program details');
            setIsLoading(false);
          }
        }
      };
      fetchProgramData();
    }
    // Priority 2: If no URL parameter but we have stored data, use that
    else if (programDetailsProps.program === null && storedProgramDetails) {
      console.log('Loading program from stored data');
      if (isMounted) {
        setProgramDetailsProps(storedProgramDetails);
        setIsLoading(false);
      }
    } else if (isMounted) {
      setIsLoading(false);
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [programIdFromUrl, currentUser]);

  const handleContextMenu = async (event: React.MouseEvent) => {
    event.preventDefault();

    if (!pdfBlob) return;

    // Get position relative to the PDF container
    const container = pdfContainerRef.current;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    try {
      // Set loading state in context menu
      setContextMenu({
        mouseX: event.clientX,
        mouseY: event.clientY,
        links: [{ url: '#', title: 'Loading all links...' }],
      });

      // Create a URL from the blob for debugging
      const pdfUrl = URL.createObjectURL(pdfBlob);
      const loadingTask = pdfjs.getDocument(pdfUrl);
      const pdf = await loadingTask.promise;
      const page = await pdf.getPage(pageNumber);

      // Get all annotations from the page
      const annotations = await page.getAnnotations();

      // Filter for link annotations
      const allLinks = annotations
        .filter((annotation) => annotation.subtype === 'Link')
        .map((link) => {
          // Ensure URL is properly formatted
          let url = link.url || '';

          // If URL doesn't have a protocol, add https://
          if (url && !url.match(/^[a-z]+:\/\//i)) {
            url = `https://${url}`;
          }

          // Extract domain for display
          let domain = '';
          try {
            domain = new URL(url).hostname;
          } catch (e) {
            domain = url;
          }

          return {
            url,
            title: link.title || domain || 'Link',
            rect: link.rect || [],
          };
        })
        .filter((link) => link.url); // Filter out links with empty URLs

      console.log('All links on current page:', allLinks);

      if (allLinks.length > 0) {
        setContextMenu({
          mouseX: event.clientX,
          mouseY: event.clientY,
          links: allLinks.map((link) => ({
            url: link.url,
            title: `${link.title} (${link.url})`,
          })),
        });
      } else {
        // No links found, show a message
        setContextMenu({
          mouseX: event.clientX,
          mouseY: event.clientY,
          links: [{ url: '#', title: 'No links found on this page' }],
        });

        // Close the menu after a delay
        setTimeout(() => {
          setContextMenu(null);
        }, 1500);
      }

      // Clean up
      URL.revokeObjectURL(pdfUrl);
    } catch (error) {
      console.error('Error extracting links:', error);
      setContextMenu(null);
    }
  };

  const handleCloseContextMenu = () => {
    setContextMenu(null);
  };

  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
    handleCloseContextMenu();
  };

  // Improved function to extract links from PDF with better page handling
  const extractLinksFromPDF = async (
    pdfBlob: Blob,
    x: number,
    y: number,
    currentPageNumber: number
  ) => {
    try {
      // Create a URL from the blob
      const pdfUrl = URL.createObjectURL(pdfBlob);

      // Load the PDF document
      const loadingTask = pdfjs.getDocument(pdfUrl);
      const pdf = await loadingTask.promise;

      // Get the current page
      const page = await pdf.getPage(currentPageNumber);

      // Get the scale from the container element
      const container = pdfContainerRef.current;
      let scale = 1.0;

      if (container) {
        const containerWidth = container.clientWidth;
        const viewport = page.getViewport({ scale: 1.0 });
        scale = containerWidth / viewport.width;
      }

      // Log all annotations for debugging
      const annotations = await page.getAnnotations();
      console.log('All PDF annotations:', annotations);

      // Log all links for debugging
      const allLinks = annotations.filter((a) => a.subtype === 'Link');
      console.log('All PDF links:', allLinks);

      // Use the utility function to extract links
      const matchedLinks = await extractLinksFromPage(page, x, y, scale);

      // Log for debugging
      console.log('Right-clicked at coordinates:', { x, y, scale });

      // If no links found but there are links in the document, try with increased tolerance
      if (matchedLinks.length === 0 && allLinks.length > 0) {
        console.log('No links found at exact position, trying with increased tolerance...');

        // Find the closest link
        const viewport = page.getViewport({ scale });
        const [pdfX, pdfY] = viewport.convertToPdfPoint(x, y);

        let closestLink = null;
        let minDistance = Infinity;

        for (const link of allLinks) {
          const rect = link.rect as [number, number, number, number];
          const linkCenterX = (rect[0] + rect[2]) / 2;
          const linkCenterY = (rect[1] + rect[3]) / 2;

          const distance = Math.sqrt((pdfX - linkCenterX) ** 2 + (pdfY - linkCenterY) ** 2);

          if (distance < minDistance && distance < 50) {
            // 50 points tolerance
            minDistance = distance;
            closestLink = link;
          }
        }

        if (closestLink) {
          console.log('Found closest link:', closestLink);
          let url = closestLink.url || '';

          // If URL doesn't have a protocol, add https://
          if (url && !url.match(/^[a-z]+:\/\//i)) {
            url = `https://${url}`;
          }

          return [
            {
              url,
              title: closestLink.title || url || 'Link',
            },
          ];
        }
      }

      // Clean up the URL object
      URL.revokeObjectURL(pdfUrl);

      return matchedLinks;
    } catch (error) {
      console.error('Error extracting links from PDF:', error);
      return [];
    }
  };

  // Update the Document component to track current page
  const onPageChange = (page: number) => {
    setPageNumber(page);
  };

  // Add a debug indicator for right-click functionality
  useEffect(() => {
    const container = pdfContainerRef.current;
    if (container) {
      // Add a subtle indicator that right-click is available
      container.title = 'Right-click to access links in the PDF';

      // Optional: Add a visual indicator on hover
      const style = document.createElement('style');
      style.innerHTML = `
        .pdf-container:hover {
          cursor: context-menu;
        }
      `;
      document.head.appendChild(style);
      container.classList.add('pdf-container');

      return () => {
        document.head.removeChild(style);
      };
    }
  }, [pdfContainerRef.current]);

  return (
    <Container className="program-detail-form" fluid>
      {isLoading ? (
        <div className="loading-container">
          <CircularProgress />
          <p>Loading program details...</p>
        </div>
      ) : loadError ? (
        <div className="error-container">
          <Alert severity="error">{loadError}</Alert>
          <Button
            className="mt-3"
            onClick={() => navigate('/dashboard/programs')}
            variant="contained"
          >
            Return to Programs List
          </Button>
        </div>
      ) : (
        <Stack alignItems="space-between" direction="column" mb={4} spacing={1}>
          <Box mb={2}>
            <Stack alignItems="center" direction="row" spacing={1}>
              <span
                className="breadcrumb-list"
                onClick={() => navigate(-1)}
                onKeyDown={(e) => e.key === 'Enter' && navigate(-1)}
                role="button"
                style={{ textDecoration: 'underline', cursor: 'pointer' }}
                tabIndex={0}
              >
                Programs
              </span>
              <span>{'>'}</span>
              <span className="breadcrumb-list" style={{ fontWeight: '700' }}>
                Program Details
              </span>
            </Stack>
          </Box>
          <Stack alignItems="center" direction="row" justifyContent="space-between" mb={2}>
            <Stack alignItems="center" direction="row" spacing={1}>
              <IconButton
                className="back-button"
                onClick={() => navigate(-1)}
                sx={{
                  width: '34px',
                  height: '34px',
                  backgroundColor: '#eff1f6',
                  borderRadius: '6px',
                  padding: '8px 16px',
                  '&:hover': {
                    backgroundColor: '#e0e3eb',
                  },
                }}
              >
                <ArrowBack />
              </IconButton>

              <h5 className="heading">Program Details #NH{program?.id} </h5>
            </Stack>
            <Stack direction="row" spacing={1}>
              {!readOnly ? (
                <>
                  <Button className="discard-btn" onClick={handlediscard} variant="light">
                    Discard
                  </Button>
                  <DeleteProgram
                    disabled={disabled}
                    onDelete={() => onSave?.()}
                    programId={program?.id}
                  />
                  <Button
                    className="d-flex justify-content-center align-items-center"
                    disabled={Object.keys(errors).length > 0}
                    onClick={save}
                  >
                    <Check size={16} />
                    &nbsp;Save
                  </Button>
                </>
              ) : (
                <>
                  <Button className="discard-btn d-flex align-items-center" variant="light">
                    <CopyToClipboard onCopy={handleCopy} text={window.location.href}>
                      <span className="d-flex align-items-center">
                        <Share />
                        &nbsp; Share
                      </span>
                    </CopyToClipboard>
                  </Button>
                  <Button
                    className="d-flex justify-content-center align-items-center"
                    onClick={() => setShowContactModal(true)}
                  >
                    Contact Millennium
                  </Button>
                </>
              )}
            </Stack>
          </Stack>

          <h6 className="sub-heading">{readOnly ? 'View' : 'Edit'} Program Details.</h6>
        </Stack>
      )}
      <Form>
        <div className="general-fields-wrapper">
          <Row>
            <Col lg={6} xs={12}>
              <h5 className="heading-gb mb-4">Grant Summary File</h5>
              <div
                className="file-uploader"
                style={{ height: files.length === 0 ? '90%' : 'auto' }}
              >
                {files?.length === 0 ? (
                  <div className="dz-message tall needsclick">
                    {!readOnly ? (
                      <Dropzone
                        disabled={readOnly}
                        getUploadParams={getUploadParams}
                        inputContent="Drag Files Here"
                        onChangeStatus={handleChangeStatus}
                      />
                    ) : (
                      <div className="no-files-message">No grant summary file available</div>
                    )}
                  </div>
                ) : (
                  <div className="pdf-file-display-container">
                    <div className="file-display-link">
                      {!files.length && <div>No files available</div>}
                      {files.length > 0 && !files[0]?.pdfUrl && <div>No PDF URL available</div>}
                      {pdfBlob && (
                        <div
                          ref={pdfContainerRef}
                          className="pdf-container"
                          onContextMenu={handleContextMenu}
                        >
                          <Document
                            externalLinkTarget="_blank"
                            file={pdfBlob}
                            loading={
                              <> </>
                              // <div className="loader-wrapper">
                              //   <div>Loading PDF...</div>
                              //   <div style={{ fontSize: '12px', marginTop: '8px' }}>
                              //     Processing PDF file...
                              //   </div>
                              // </div>
                            }
                            onLoadError={(error) => {
                              console.error('PDF Load Error:', error);
                              setPdfError(error.message);
                            }}
                            onLoadSuccess={onDocumentLoadSuccess}
                          >
                            {numPages &&
                              Array.from(new Array(numPages), (el, index) => (
                                <Page
                                  key={`page_${index + 1}`}
                                  onGetAnnotationsSuccess={(annotations) => {
                                    // Log available links for debugging
                                    const links = annotations.filter((a) => a.subtype === 'Link');
                                    if (links.length > 0 && index + 1 === pageNumber) {
                                      console.debug(`Page ${index + 1} has ${links.length} links`);
                                    }
                                  }}
                                  onGetTextSuccess={() => {
                                    // Update current page when rendering is complete
                                    if (index + 1 === pageNumber) {
                                      setPageNumber(index + 1);
                                    }
                                  }}
                                  onLoadError={(error) =>
                                    console.error(`Error loading page ${index + 1}:`, error)
                                  }
                                  onRenderError={(error) =>
                                    console.error(`Error rendering page ${index + 1}:`, error)
                                  }
                                  pageNumber={index + 1}
                                />
                              ))}
                          </Document>
                        </div>
                      )}
                      {pdfError !== '' && (
                        <div style={{ color: 'red', marginTop: '10px' }}>
                          Error loading PDF: {pdfError}
                        </div>
                      )}
                    </div>
                    <Button className="expand-icon" onClick={handleExpandPdf} variant="light">
                      <ZoomOutMap />
                    </Button>
                  </div>
                )}
              </div>
              {!(files?.length === 0) && files[0].pdfName && (
                <Stack
                  alignItems="center"
                  direction="row"
                  sx={{
                    justifyContent: 'flex-start',
                    textAlign: 'center',
                    color: '#2C4474',
                    gap: '5px',
                    marginTop: '20px',
                  }}
                >
                  <InsertDriveFileOutlinedIcon sx={{ fontSize: 20 }} />
                  <a
                    download={files[0].pdfName}
                    href={files[0].pdfUrl}
                    rel="noopener noreferrer"
                    style={{ textDecoration: 'none', color: 'inherit' }}
                    target="_blank"
                  >
                    <Typography sx={{ cursor: 'pointer' }} variant="body2">
                      {files[0]?.pdfName}
                    </Typography>
                  </a>
                  {currentUser.isMillenniumUser && currentUser.userType !== 'millenniumAnalyst' && (
                    <IconButton
                      onClick={() => handleDeleteFile(files[0]?.id)}
                      size="small"
                      sx={{ color: '#2C4474' }}
                    >
                      <Close />
                    </IconButton>
                  )}
                </Stack>
              )}
            </Col>
            <Col lg={6} xs={12}>
              <h5 className="heading-gb mb-4">Required Fields</h5>
              {!readOnly ? (
                <FunderInput
                  className="common-input"
                  controlId="createProgram.Funder"
                  defaultValue={program?.funder}
                  disabled={readOnly}
                  hideRequiredIndicator
                  onChange={(newValue) => setField('funder', newValue)}
                  placeholder="Start typing a name..."
                />
              ) : (
                <div className="field-heading-wraper">
                  <div className="field-heading">Funder</div>
                  <div className="field-value">{program?.funder}</div>
                </div>
              )}

              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <TextInput
                      className="common-input"
                      controlId="createProgram.Name"
                      defaultValue={program?.name}
                      disabled={readOnly}
                      errors={errors.name}
                      hideRequiredIndicator
                      label="Grant Program Name"
                      onChange={(newValue) => setField('name', newValue)}
                      placeholder="My Grant Program"
                      required
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Grant Program Name</div>
                      <div className="field-value">{program?.name}</div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      choices={OrgTypes}
                      className="common-input"
                      controlId="createProgram.OrgTypes"
                      defaultValue={program?.orgTypes || []}
                      disabled={readOnly}
                      isIndexBased={false}
                      label="Organization Type"
                      onChange={(selectedIndices) => {
                        const selectedOrgTypes = selectedIndices.map((index) => OrgTypes[index]);
                        setField('orgTypes', selectedOrgTypes);
                      }}
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Organization Types</div>
                      <div className="field-value selectorbg">
                        {Array.isArray(program?.orgTypes)
                          ? program.orgTypes.join(', ')
                          : program?.orgTypes || ''}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <DateField
                      className="common-input"
                      controlId="createProgram.StartDate"
                      disabled={readOnly}
                      errors={errors.startsAt}
                      hideRequiredIndicator
                      label="Start Date"
                      onChange={(newValue) => setField('startsAt', newValue)}
                      value={form.startsAt}
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Start Date</div>
                      <div className="field-value">
                        {program?.startsAt
                          ? new Date(program?.startsAt).toLocaleDateString('en-US')
                          : ''}
                      </div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <DateField
                      className="common-input"
                      controlId="createProgram.DueDate"
                      disabled={readOnly}
                      errors={errors.endsAt}
                      hideRequiredIndicator
                      label="Due Date"
                      onChange={(newValue) => setField('endsAt', newValue)}
                      popperClassName="date-field-popper-container"
                      value={reduceTZ(form.endsAt?.toLocaleString(), '', 'YYYY-MM-DD')}
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">End Date</div>
                      <div className="field-value">
                        {program?.endsAt
                          ? new Date(program?.endsAt).toLocaleDateString('en-US')
                          : ''}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      key={`category-select-${JSON.stringify(form.category)}`}
                      choices={applicationsConfigs.alphabetSortedCategories}
                      className="common-input"
                      controlId="createProgram.Category"
                      defaultValue={
                        Array.isArray(form.category)
                          ? form.category
                          : form.category !== undefined && form.category !== null
                            ? [form.category]
                            : []
                      }
                      disabled={readOnly}
                      hideRequiredIndicator
                      isIndexBased={false}
                      label="Category"
                      onChange={(selectedIndices) => {
                        console.log('Category selection changed:', selectedIndices);
                        const selectedCategories = selectedIndices.map(
                          (index) => applicationsConfigs.alphabetSortedCategories[index]
                        );
                        console.log('Selected categories:', selectedCategories);
                        setForm((prevForm) => ({
                          ...prevForm,
                          category: selectedCategories,
                        }));
                      }}
                      required
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Category </div>
                      <div className="field-value selectorbg">
                        {getCategoryDisplayValues(program?.category).join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      choices={applicationsConfigs.sources}
                      className="common-input"
                      controlId="createProgram.Source"
                      defaultValue={
                        Array.isArray(program?.source)
                          ? program.source
                          : program?.source !== undefined && program?.source !== null
                            ? [program.source]
                            : []
                      }
                      disabled={readOnly}
                      hideRequiredIndicator
                      label="Source"
                      onChange={useCallback((selectedIndices: number[]) => {
                        setField('source', selectedIndices);
                      }, [])}
                      required
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Source</div>
                      <div className="field-value selectorbg">
                        {getSourceDisplayValues(program?.source).join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      key={`states-${Date.now()}`}
                      choices={statesWithAllOption.map((state) => state.name)}
                      className="common-input"
                      controlId="programDetails.States"
                      defaultValue={form.states || []}
                      disabled={readOnly}
                      isIndexBased={false}
                      label="State"
                      onChange={useCallback(
                        (selectedIndices) => {
                          // Using useCallback to prevent recreation on each render
                          const selectedStates = selectedIndices.map(
                            (index) => statesWithAllOption[index].name
                          );

                          // If "All States" is selected, clear any other selections
                          const hasAllStates = selectedStates.includes(ALL_STATES_OPTION);
                          const finalSelection = hasAllStates
                            ? [ALL_STATES_OPTION]
                            : selectedStates;

                          // IMPORTANT: Always set to an array, never undefined
                          const safeSelection = finalSelection || [];

                          // Update form state directly to ensure consistency
                          setForm((prevForm) => ({
                            ...prevForm,
                            states: safeSelection,
                            // Reset counties when states change
                            counties: hasAllStates ? [ALL_COUNTIES_OPTION] : [],
                          }));
                        },
                        [statesWithAllOption]
                      )} // Dependencies for useCallback
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">States</div>
                      <div className="field-value selectorbg">
                        {!program?.states ||
                        program.states.length === 0 ||
                        program.states.includes(ALL_STATES_OPTION)
                          ? 'All States'
                          : program.states.join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      key={`counties-${Date.now()}`}
                      choices={getCountiesForStates(
                        (form.states || [])
                          .filter((state) => state !== ALL_STATES_OPTION)
                          .map((stateName) => states.find((s) => s.name === stateName)?.abbr)
                          .filter(Boolean) || []
                      )}
                      className="common-input"
                      controlId="programDetails.Counties"
                      defaultValue={form.counties || []}
                      disabled={
                        readOnly ||
                        !(form.states || []).length ||
                        (form.states || []).includes(ALL_STATES_OPTION)
                      }
                      isIndexBased={false}
                      label="County"
                      onChange={useCallback(
                        (selectedIndices) => {
                          // Using useCallback to prevent recreation on each render
                          const availableCounties = getCountiesForStates(
                            (form.states || [])
                              .filter((state) => state !== ALL_STATES_OPTION)
                              .map((stateName) => states.find((s) => s.name === stateName)?.abbr)
                              .filter(Boolean) || []
                          );

                          const selectedCounties = selectedIndices.map(
                            (index) => availableCounties[index]
                          );

                          // If "All Counties" is selected, clear any other selections
                          const hasAllCounties = selectedCounties.includes(ALL_COUNTIES_OPTION);
                          const finalSelection = hasAllCounties
                            ? [ALL_COUNTIES_OPTION]
                            : selectedCounties;

                          // IMPORTANT: Always set to an array, never undefined
                          const safeSelection = finalSelection || [];

                          // Update form state directly
                          setForm((prevForm) => ({
                            ...prevForm,
                            counties: safeSelection,
                          }));
                        },
                        [form.states]
                      )} // Dependencies for useCallback
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Counties</div>
                      <div className="field-value selectorbg">
                        {!program?.counties ||
                        program.counties.length === 0 ||
                        program.counties.includes(ALL_COUNTIES_OPTION)
                          ? 'All Counties'
                          : program.counties.join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MoneyInputNew
                      canVary
                      className="common-input"
                      controlId="createProgram.FundingAmount"
                      defaultAmount={program?.fundingAmount}
                      defaultStringAmount={program?.varyingFundingAmount}
                      defaultVaries={program?.amountVaries}
                      disabled={readOnly}
                      errors={errors.fundingAmount}
                      hideRequiredIndicator
                      isFromPrograms={isFromPrograms}
                      label="Funding Amount"
                      onAmountChange={(newAmount) => setField('fundingAmount', newAmount)}
                      onVaryChange={(newVaries) => setField('amountVaries', newVaries)}
                      onVaryingAmountChange={(newAmount) =>
                        setField('varyingFundingAmount', newAmount)
                      }
                      required
                      text=""
                      textClass="currency-text"
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Funding Amount</div>
                      <div className="field-value">
                        {program?.amountVaries
                          ? program?.varyingFundingAmount || 'Varies'
                          : formatCurrencyValue(program?.fundingAmount)}
                      </div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <TextInput
                      className="common-input"
                      controlId="createProgram.MatchRequirements"
                      defaultValue={program?.matchRequirements}
                      disabled={readOnly}
                      hideRequiredIndicator
                      label="Match Requirements"
                      onChange={(newValue) => setField('matchRequirements', newValue)}
                      placeholder="25% Match"
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Match Requirements</div>
                      <div className="field-value">{program?.matchRequirements}</div>
                    </div>
                  )}
                </Col>
              </Row>

              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <TextInput
                      className="common-input"
                      controlId="createProgram.PeriodOfPerformance"
                      defaultValue={program?.performancePeriod}
                      disabled={readOnly}
                      hideRequiredIndicator
                      label="Period of Performance"
                      onChange={(newValue) => setField('performancePeriod', newValue)}
                      placeholder="Enter Period of Performance"
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Period of Performance</div>
                      <div className="field-value">{program?.performancePeriod}</div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <TextInput
                      className="common-input"
                      controlId="createProgram.EstimatedResponse"
                      defaultValue={program?.estimatedResponse}
                      disabled={readOnly}
                      hideRequiredIndicator
                      label="Estimated Response"
                      onChange={(newValue) => setField('estimatedResponse', newValue)}
                      placeholder="Enter Estimated Response"
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Estimated Response</div>
                      <div className="field-value">{program?.estimatedResponse}</div>
                    </div>
                  )}
                </Col>
              </Row>
              {currentUser.isMillenniumUser && (
                <Row>
                  <Col lg={6} xs={12}>
                    {!readOnly ? (
                      <div className="program-common-input">
                        <div className="d-flex flex-column mb-2">
                          <span className="show-for-clients-text small">Show for Flex Clients</span>
                          <CustomInput
                            checked={form.showForFlexClient ?? true}
                            className="show-flexclient-toggle"
                            id="showForFlexClient-toggle"
                            label=""
                            name="showForFlexClient"
                            onChange={(e) => setField('showForFlexClient', e.target.checked)}
                            type="switch"
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="field-heading-wraper">
                        <div className="field-heading">Show for Flex Clients</div>
                        <div className="field-value selectorbg">
                          {program?.showForFlexClient ? 'Yes' : 'No'}
                        </div>
                      </div>
                    )}
                  </Col>
                </Row>
              )}
            </Col>
          </Row>
        </div>
        {!readOnly && (
          <div className="mt-4 custom-fields-wrapper">
            <div
              className="hide-show-wrapper"
              onClick={toggleSectionVisibility}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  toggleSectionVisibility();
                }
              }}
              role="button"
              tabIndex={0}
            >
              <h5 className="heading-gb mb-4">Custom Fields</h5>
              <span className="ms-2" style={{ display: 'flex', alignItems: 'center' }}>
                {isSectionVisible ? (
                  <>
                    <KeyboardArrowUp
                      className="ms-2"
                      style={{ color: '#2C4474', fontSize: '18px' }}
                    />
                    <span className="show-hide-text">Hide Section</span>
                  </>
                ) : (
                  <>
                    <KeyboardArrowDown
                      className="ms-2"
                      style={{ color: '#2C4474', fontSize: '18px' }}
                    />{' '}
                    <span className="show-hide-text">Show Section</span>
                  </>
                )}
              </span>
            </div>
            {isSectionVisible && (
              <>
                <hr />
                <CustomFields
                  defaultFields={
                    typeof program?.customFields === 'string'
                      ? JSON.parse(program?.customFields)
                      : program?.customFields
                  }
                  disabled={readOnly}
                  onChange={(newFields) => setField('customFields', newFields)}
                />
              </>
            )}
          </div>
        )}
      </Form>
      <ContactMillenniumModal onHide={() => setShowContactModal(false)} show={showContactModal} />
      <Modal onClose={() => setShowPdfModal(false)} open={showPdfModal}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '80%',
            maxWidth: '1200px',
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
          }}
        >
          <Stack alignItems="center" direction="row" justifyContent="space-between" sx={{ mb: 3 }}>
            <Typography component="h2" variant="h6">
              Grant Summary File (PDF) Full View
            </Typography>

            <Stack alignItems="center" direction="row" spacing={2}>
              <Button
                className="discard-btn"
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  handleCopy();
                }}
                variant="light"
              >
                <span className="d-flex align-items-center">
                  <Share />
                  &nbsp; Share
                </span>
              </Button>

              <Button
                onClick={() => {
                  setShowPdfModal(false);
                  setTimeout(() => {
                    setShowContactModal(true);
                  }, 300);
                }}
              >
                Contact Millennium
              </Button>

              <IconButton onClick={() => setShowPdfModal(false)} sx={{ ml: 2 }}>
                <Close />
              </IconButton>
            </Stack>
          </Stack>
          <Box
            ref={pdfContainerRef}
            className="pdf-container"
            onContextMenu={handleContextMenu}
            sx={{
              maxHeight: '70vh',
              overflowY: 'auto',
              border: '1px solid #ddd',
              borderRadius: 1,
              p: 2,
            }}
          >
            <Document file={pdfBlob} onLoadSuccess={onDocumentLoadSuccess}>
              {Array.from(new Array(numPages), (el, index) => (
                <Page
                  key={index}
                  onGetAnnotationsSuccess={(annotations) => {
                    // Log available links for debugging
                    const links = annotations.filter((a) => a.subtype === 'Link');
                    if (links.length > 0 && index + 1 === pageNumber) {
                      console.debug(`Page ${index + 1} has ${links.length} links`);
                    }
                  }}
                  onGetTextSuccess={() => {
                    // Update current page when rendering is complete
                    if (index + 1 === pageNumber) {
                      setPageNumber(index + 1);
                    }
                  }}
                  pageNumber={index + 1}
                />
              ))}
            </Document>
          </Box>
        </Box>
      </Modal>
      <Menu
        anchorPosition={
          contextMenu !== null ? { top: contextMenu.mouseY, left: contextMenu.mouseX } : undefined
        }
        anchorReference="anchorPosition"
        onClose={handleCloseContextMenu}
        open={contextMenu !== null}
        PaperProps={{
          sx: {
            maxWidth: '400px',
            maxHeight: '300px',
            overflowY: 'auto',
          },
        }}
      >
        {contextMenu?.links.length === 0 ? (
          <MenuItem disabled>
            <Typography variant="body2">No links found on this page</Typography>
          </MenuItem>
        ) : (
          <>
            {contextMenu?.links.length > 1 && (
              <MenuItem disabled>
                <Typography fontWeight="bold" variant="body2">
                  All links on this page ({contextMenu?.links.length})
                </Typography>
              </MenuItem>
            )}
            {contextMenu?.links.map((link, index) => (
              <MenuItem
                key={index}
                disabled={link.url === '#'}
                onClick={() => handleLinkClick(link.url)}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '8px 16px',
                  borderBottom: index < contextMenu.links.length - 1 ? '1px solid #eee' : 'none',
                }}
              >
                <Stack alignItems="center" direction="row" spacing={1} sx={{ width: '100%' }}>
                  {link.url === '#' ? (
                    <CircularProgress size={16} sx={{ mr: 1 }} />
                  ) : (
                    <LinkIcon fontSize="small" sx={{ mr: 1 }} />
                  )}
                  <Typography
                    noWrap
                    sx={{
                      fontWeight: 'bold',
                      maxWidth: '320px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                    variant="body2"
                  >
                    {link.title.split('(')[0]}
                  </Typography>
                </Stack>
                {link.url !== '#' && (
                  <Typography
                    color="text.secondary"
                    noWrap
                    sx={{
                      fontSize: '0.75rem',
                      maxWidth: '320px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      ml: 4,
                    }}
                    variant="body2"
                  >
                    {link.url}
                  </Typography>
                )}
              </MenuItem>
            ))}
          </>
        )}
      </Menu>
    </Container>
  );
}
