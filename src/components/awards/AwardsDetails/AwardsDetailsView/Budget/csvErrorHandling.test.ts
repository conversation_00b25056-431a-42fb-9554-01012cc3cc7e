/**
 * Test file to demonstrate enhanced CSV error handling
 * This shows how the new error messages provide detailed line-by-line feedback
 */

// Mock CSV data with various error scenarios
const mockCsvDataWithErrors = [
  {
    csvID: '1',
    Name: 'Personnel',
    'Total Project Budget': '50000',
    'Award Expended': '10000',
    'Award Balance': '40000',
    'Match Amount': '5000',
    'Match Expended': '1000',
    'Match Balance': '4000',
    _lineNumber: 2,
    _rawLine: '1,,Personnel,50000,10000,40000,5000,1000,4000'
  },
  {
    csvID: '2',
    Name: '', // Missing name - should trigger error
    'Total Project Budget': '30000',
    'Award Expended': '5000',
    'Award Balance': '25000',
    'Match Amount': '3000',
    'Match Expended': '500',
    'Match Balance': '2500',
    _lineNumber: 3,
    _rawLine: '2,,,30000,5000,25000,3000,500,2500'
  },
  {
    csvID: '3',
    Name: 'Equipment',
    'Total Project Budget': 'invalid-number', // Invalid number - should trigger error
    'Award Expended': '2000',
    'Award Balance': '8000',
    'Match Amount': '1000',
    'Match Expended': '200',
    'Match Balance': '800',
    _lineNumber: 4,
    _rawLine: '3,,Equipment,invalid-number,2000,8000,1000,200,800'
  },
  {
    csvID: '4',
    Name: 'Travel',
    'Total Project Budget': '15000',
    'Award Expended': '8000',
    'Award Balance': '10000', // Math doesn't add up - should trigger error
    'Match Amount': '1500',
    'Match Expended': '800',
    'Match Balance': '700',
    _lineNumber: 5,
    _rawLine: '4,,Travel,15000,8000,10000,1500,800,700'
  },
  {
    csvID: '',
    'Parent ID': '99', // Non-existent parent ID - should trigger error
    Name: 'Sub-item',
    'Total Project Budget': '5000',
    'Award Expended': '1000',
    'Award Balance': '4000',
    'Match Amount': '500',
    'Match Expended': '100',
    'Match Balance': '400',
    _lineNumber: 6,
    _rawLine: ',99,Sub-item,5000,1000,4000,500,100,400'
  }
];

// Expected error messages that should be generated
const expectedErrorMessages = [
  "CSV parsing failed at line 3: '2,,,30000,5000,25000,3000,500,2500'. The required 'Name' field is missing or empty.",
  "CSV parsing failed at line 4: '3,,Equipment,invalid-number,2000,8000,1000,200,800'. The 'Total Project Budget' field contains 'invalid-number' which is not a valid number.",
  "CSV parsing failed at line 5: '4,,Travel,15000,8000,10000,1500,800,700'. Budget calculation error for \"Travel\": Total Budget (15000) does not equal Award Expended (8000) + Award Balance (10000). Please verify the calculations.",
  "CSV parsing failed at line 6: ',99,Sub-item,5000,1000,4000,500,100,400'. Row \"Sub-item\" references non-existent parent ID: 99."
];

/**
 * Example of how the enhanced error handling works:
 * 
 * Before (generic error):
 * "Failed to parse CSV file. Please check the file format."
 * 
 * After (detailed errors):
 * - "CSV parsing failed at line 3: '2,,,30000,5000,25000,3000,500,2500'. The required 'Name' field is missing or empty."
 * - "CSV parsing failed at line 4: '3,,Equipment,invalid-number,2000,8000,1000,200,800'. The 'Total Project Budget' field contains 'invalid-number' which is not a valid number."
 * - "CSV parsing failed at line 5: '4,,Travel,15000,8000,10000,1500,800,700'. Budget calculation error for \"Travel\": Total Budget (15000) does not equal Award Expended (8000) + Award Balance (10000). Please verify the calculations."
 * - "CSV parsing failed at line 6: ',99,Sub-item,5000,1000,4000,500,100,400'. Row \"Sub-item\" references non-existent parent ID: 99."
 * 
 * This provides users with:
 * 1. Exact line numbers where errors occur
 * 2. Preview of the problematic line content
 * 3. Specific field-level error descriptions
 * 4. Actionable guidance on how to fix the issues
 */

export { mockCsvDataWithErrors, expectedErrorMessages };
