/**
 * Demonstration of Enhanced CSV Error Handling
 * This file shows the before/after comparison of error messages
 */

// Sample problematic CSV data that would trigger various errors
export const problematicCsvData = [
  {
    csvID: '1',
    Name: 'Personnel',
    'Total Project Budget': '50000',
    'Award Expended': '10000',
    'Award Balance': '40000',
    'Match Amount': '5000',
    'Match Expended': '1000',
    'Match Balance': '4000',
    _lineNumber: 2,
    _rawLine: '1,,Personnel,50000,10000,40000,5000,1000,4000'
  },
  {
    csvID: '2',
    Name: '', // ERROR: Missing required name
    'Total Project Budget': '30000',
    'Award Expended': '5000',
    'Award Balance': '25000',
    'Match Amount': '3000',
    'Match Expended': '500',
    'Match Balance': '2500',
    _lineNumber: 3,
    _rawLine: '2,,,30000,5000,25000,3000,500,2500'
  },
  {
    csvID: '3',
    Name: 'Equipment',
    'Total Project Budget': 'not-a-number', // ERROR: Invalid number
    'Award Expended': '2000',
    'Award Balance': '8000',
    'Match Amount': '1000',
    'Match Expended': '200',
    'Match Balance': '800',
    _lineNumber: 4,
    _rawLine: '3,,Equipment,not-a-number,2000,8000,1000,200,800'
  },
  {
    csvID: '4',
    Name: 'Travel',
    'Total Project Budget': '15000',
    'Award Expended': '8000',
    'Award Balance': '10000', // ERROR: Math doesn't add up (8000 + 10000 ≠ 15000)
    'Match Amount': '1500',
    'Match Expended': '800',
    'Match Balance': '700',
    _lineNumber: 5,
    _rawLine: '4,,Travel,15000,8000,10000,1500,800,700'
  },
  {
    csvID: '',
    'Parent ID': '99', // ERROR: Non-existent parent ID
    Name: 'Sub-item',
    'Total Project Budget': '5000',
    'Award Expended': '1000',
    'Award Balance': '4000',
    'Match Amount': '500',
    'Match Expended': '100',
    'Match Balance': '400',
    _lineNumber: 6,
    _rawLine: ',99,Sub-item,5000,1000,4000,500,100,400'
  },
  {
    csvID: '5',
    Name: 'Supplies',
    'Total Project Budget': '12000',
    'Award Expended': '-500', // ERROR: Negative value
    'Award Balance': '12500',
    'Match Amount': '1200',
    'Match Expended': '100',
    'Match Balance': '1100',
    _lineNumber: 7,
    _rawLine: '5,,Supplies,12000,-500,12500,1200,100,1100'
  }
];

// What users would see BEFORE the enhancement
export const oldErrorMessage = "Failed to parse CSV file. Please check the file format.";

// What users see AFTER the enhancement
export const newErrorMessages = [
  "CSV parsing failed at line 3: '2,,,30000,5000,25000,3000,500,2500'. The required 'Name' field is missing or empty.",
  
  "CSV parsing failed at line 4: '3,,Equipment,not-a-number,2000,8000,1000,200,800'. The 'Total Project Budget' field contains 'not-a-number' which is not a valid number.",
  
  "CSV parsing failed at line 5: '4,,Travel,15000,8000,10000,1500,800,700'. Budget calculation error for \"Travel\": Total Budget (15000) does not equal Award Expended (8000) + Award Balance (10000). Please verify the calculations.",
  
  "CSV parsing failed at line 6: ',99,Sub-item,5000,1000,4000,500,100,400'. Row \"Sub-item\" references non-existent parent ID: 99.",
  
  "CSV parsing failed at line 7: '5,,Supplies,12000,-500,12500,1200,100,1100'. The 'Award Expended' field contains a negative value: -500."
];

/**
 * Benefits of the Enhanced Error Handling:
 * 
 * 1. SPECIFIC LINE IDENTIFICATION
 *    - Users know exactly which line has the problem
 *    - No more guessing or manual line counting
 * 
 * 2. VISUAL LINE PREVIEW
 *    - Shows the actual problematic data
 *    - Users can quickly spot the issue
 * 
 * 3. FIELD-LEVEL PRECISION
 *    - Identifies the exact field that's problematic
 *    - Explains what's wrong with that specific field
 * 
 * 4. ACTIONABLE GUIDANCE
 *    - Tells users exactly what to fix
 *    - Provides context for why it's an error
 * 
 * 5. COMPREHENSIVE VALIDATION
 *    - Catches multiple types of errors in one pass
 *    - Validates data integrity and relationships
 */

export const errorHandlingComparison = {
  before: {
    userExperience: "Frustrating - generic error with no guidance",
    timeToFix: "Long - requires manual inspection of entire file",
    supportTickets: "High - users need help understanding what's wrong"
  },
  after: {
    userExperience: "Clear - specific errors with exact locations",
    timeToFix: "Fast - direct navigation to problematic lines",
    supportTickets: "Low - users can self-diagnose and fix issues"
  }
};
