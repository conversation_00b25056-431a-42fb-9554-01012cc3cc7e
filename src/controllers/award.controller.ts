import { Request, Response } from 'express';
import awardService from '../services/award.service';
import { AwardInput, AwardOuput, AwardStatus } from '../models/types/award';
import notificationService from '../services/notification.service';
import { sourceTypes } from '../models/types/award-notification';
import { FilterNotifications } from './types/notification.type';
import db from '../models';

require('dotenv').config();

const httpStatus = require('http-status');
const dayjs = require('dayjs');
const advancedFormat = require('dayjs/plugin/advancedFormat');

const catchAsync = require('../helpers/catch-async');

const strings = require('../config/strings');

dayjs.extend(advancedFormat);

const {
  users: { userTypes },
} = strings;
const clientAdmin = `${userTypes.clientAdmin}`;
const clientAnalyst = `${userTypes.clientAnalyst}`;
const millenniumManager = `${userTypes.millenniumManager}`;
const millenniumResearcher = `${userTypes.millenniumResearcher}`;

const awardStatuses = strings.awards.statusStrings;

const { createFile, deleteFile, getFiles } = require('./awards/award-file.controller');
const {
  addReport,
  updateReport,
  getReports,
  deleteReport,
} = require('./awards/award-report.controller');

const { Award } = db;

const {
  addBudgetEntry,
  getBudgetEntries,
  updateBudgetEntry,
  deleteBudgetEntry,
} = require('./awards/award-budget.controller');

const {
  addPayment,
  updatePayment,
  listPayments,
  getPayment,
  deletePayment,
} = require('./awards/award-payment.controller');

const {
  listUserRoles,
  getUserRole,
  createUserRoles,
  updateUserRoles,
  deleteUserRoles,
} = require('./awards/award-user-roles.controller');
const { calculateClosedStatus } = require('../services/awards/award-timeline.service');

const list = catchAsync(async (req: Request, res: Response) => {
  const { user } = req;

  let clientId;

  if (req.query?.status === 'appropriated')
    req.query.status = [
      awardStatuses.budgetRequired,
      awardStatuses.reportScheduleRequired,
      awardStatuses.contractMaterialRequired,
    ];

  if ([clientAdmin, clientAnalyst].includes(user.userType)) {
    if (user.client_creator_id === null)
      return res.status(httpStatus.FORBIDDEN).json({ message: 'Forbidden' });

    clientId = user.clientCreatorId;
  }

  const result = await awardService.list(
    { ...req.query, ...(clientId && { clients: clientId }) },
    user
  );

  return res.status(httpStatus.OK).json(result);
});

const search = catchAsync(async (req: Request, res: Response) => {
  if (req.query?.status === 'appropriated')
    req.query.status = [
      awardStatuses.budgetRequired,
      awardStatuses.reportScheduleRequired,
      awardStatuses.contractMaterialRequired,
    ];

  const { field, query, clientId } = req.query as {
    field: keyof AwardInput;
    query: string;
    clientId?: string;
  };
  const { user } = req;

  let results = await awardService.search(
    field,
    query,
    user,
    clientId ? parseInt(clientId, 10) : undefined
  );

  if ([millenniumManager, millenniumResearcher].includes(user.userType)) {
    const userClients = user.get('applicationClients') || [];

    if (userClients.length === 0) return res.status(httpStatus.OK).json([]);

    results = results.filter((result: AwardOuput) => userClients.includes(result.clientId));
  }

  return res.status(httpStatus.OK).json(results);
});

const getById = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { user } = req;
  const award = await awardService.getById(id, user);

  return res.status(httpStatus.OK).json(award);
});

const createOne = catchAsync(async (req: Request, res: Response) => {
  const fields = {
    ...req.body,
    assigneeId: req.body.assigneeId || req.user.id,
  };
  const newAward = await awardService.createOne(fields, req.user);

  return res.status(httpStatus.CREATED).json(newAward);
});

const updateOne = catchAsync(async (req: Request, res: Response) => {
  await awardService.updateOne(req.body, req.user);
  return res.sendStatus(httpStatus.OK);
});

const requestApproval = catchAsync(async (req: Request, res: Response) => {
  const { note } = req.body;
  const { id } = req.params;
  await awardService.requestApproval(parseInt(id, 10), note, req.user?.id);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const setCompliance = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status }: { status?: 'closed' | 'compliance' | AwardStatus } = req.body;
  const awardId = parseInt(id, 10);
  const award = await Award.findByPk(awardId);
  const isClosed = await calculateClosedStatus(award, awardId);
  if (status) {
    if (isClosed) {
      await awardService.updateAwardStatus(awardId, 'closed');
      return res.sendStatus(httpStatus.OK);
    }
  } else {
    await awardService.updateAwardStatus(awardId, 'compliance');
    return res.sendStatus(httpStatus.NO_CONTENT);
  }
  return res
    .status(httpStatus.BAD_REQUEST)
    .json({ message: 'Cannot move to Closed please check for payments and budget detail.' });
});

const approveAward = catchAsync(async (req: Request, res: Response) => {
  const { note } = req.body;
  const { id } = req.params;
  await awardService.approveAward(parseInt(id, 10), note);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const rejectAward = catchAsync(async (req: Request, res: Response) => {
  const { note } = req.body;
  const { id } = req.params;
  await awardService.rejectAward(parseInt(id, 10), note);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const deleteOne = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.body;
  await awardService.deleteOne(id);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const getAwardTimelines = catchAsync(async (req: Request, res: Response) => {
  const { id: awardId } = req.params;

  const timelines = await awardService.getAwardTimelines(awardId);
  return res.status(httpStatus.OK).json(timelines);
});

const exportCsv = catchAsync(async (req: Request, res: Response) => {
  const compressedFile = await awardService.generateCsv(req.query, req.user);

  // Set proper headers for CSV file download (compressed as ZIP)
  res.setHeader('Content-Type', 'application/zip');
  res.setHeader('Content-Disposition', 'attachment; filename=awards.zip');

  return res.send(compressedFile);
});

const exportXlxs = catchAsync(async (req: Request, res: Response) => {
  const file = await awardService.generateXlxs(req.query, req.user);

  // Set proper headers for XLSX file download
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=awards.xlsx');

  return res.send(file);
});

const getAwardsByUserId = catchAsync(async (req: Request, res: Response) => {
  const { userId } = req.params;

  const awards = await awardService.getAwardsByUserId(userId, req.query);

  return res.status(httpStatus.OK).json(awards);
});

const listNotifications = catchAsync(async (req: Request, res: Response) => {
  const { query } = req;

  query.sourceType = query.sourceType || (sourceTypes as any as string[]);
  query.awardId = req.params.awardId;

  const data = await notificationService.list({
    ...(query as FilterNotifications),
  });

  return res.status(httpStatus.OK).json(data);
});

export const createVersion = catchAsync(async (req: Request, res: Response) => {
  const { awardId } = req.params;
  const { versionName, entries } = req.body;

  const result = await awardService.createVersion(
    versionName,
    parseInt(awardId, 10),
    entries,
    req.user
  );

  return res.status(httpStatus.CREATED).json(result);
});

export const getVersionsByAwardId = catchAsync(async (req: Request, res: Response) => {
  const { awardId } = req.params;
  const page = parseInt(req.query.page as string) || 1;
  const pageSize = parseInt(req.query.pageSize as string) || 50;

  const versions = await awardService.getVersionsByAwardId(awardId, page, pageSize);

  return res.status(httpStatus.OK).json(versions);
});

export const getVersionById = catchAsync(async (req: Request, res: Response) => {
  const { versionId } = req.params;
  const version = await awardService.getVersionById(versionId);

  return res.status(httpStatus.OK).json(version);
});
module.exports = {
  list,
  search,
  getAwardsByUserId,
  getById,
  createOne,
  updateOne,
  requestApproval,
  setCompliance,
  approveAward,
  rejectAward,
  deleteOne,

  // Budget Entries
  addBudgetEntry,
  getBudgetEntries,
  updateBudgetEntry,
  deleteBudgetEntry,

  // Award Files
  createFile,
  deleteFile,
  getFiles,

  // Award Timelines
  getAwardTimelines,

  // Award Reports
  addReport,
  updateReport,
  getReports,
  deleteReport,

  // Data Exports,
  exportCsv,
  exportXlxs,

  // Award Payments
  addPayment,
  updatePayment,
  listPayments,
  getPayment,
  deletePayment,

  // User Roles
  listUserRoles,
  getUserRole,
  createUserRoles,
  updateUserRoles,
  deleteUserRoles,

  // Notifications
  listNotifications,

  // Award Version
  createVersion,
  getVersionsByAwardId,
  getVersionById,
};
