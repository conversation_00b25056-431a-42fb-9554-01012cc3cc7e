import { Request, Response } from 'express';
import db from '../models';

import { ClientOuput } from '../models/types/client';

require('dotenv').config();

const httpStatus = require('http-status');
const dayjs = require('dayjs');
const advancedFormat = require('dayjs/plugin/advancedFormat');

const catchAsync = require('../helpers/catch-async');

const { ProgramFiles } = db;
const { applicationService } = require('../services');

const strings = require('../config/strings');

dayjs.extend(advancedFormat);

const {
  users: { userTypes },
} = strings;
const clientAdmin = `${userTypes.userAdmin}`;
const clientAnalyst = `${userTypes.userAnalyst}`;

const list = catchAsync(async (req: Request, res: Response) => {
  /*
    MIL-251
    The query parameter `sortBy` accepts various columns to sort the content by.
    Possible values: assignee, client, funder, programName, startDate, and dueDate.

    The query parameter `sortOrder` accepts different methods of listing sorted content.
    Possible values: asc, desc

    (`asc` for dates is 'Oldest to Newest'; `desc` for dates is 'Newest to Oldest')
    (`asc` for `funder` and `programName` is '0 to 9, A to Z, then a to z'; `desc` for these fields are 'Z to A, then z to a, then 9 to 0')

    Null values will always be below everything else.
  */
  const { user } = req;

  let clientId;
  if ([clientAdmin, clientAnalyst].includes(user.userType)) {
    if (user.client_creator_id === null) {
      return res.status(httpStatus.FORBIDDEN).json({ message: 'Forbidden' });
    }

    clientId = user.clientCreatorId;
  }

  const result = await applicationService.list(
    {
      ...req.query,
      ...(clientId && { clients: clientId }),
    },
    req.user
  );

  return res.status(httpStatus.OK).json(result);
});

const search = catchAsync(async (req: Request, res: Response) => {
  const { field, query } = req.query;
  const results = await applicationService.search(field, query, req.user);

  return res.status(httpStatus.OK).json(results);
});

const getById = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const application = await applicationService.getById(id);
  return res.status(httpStatus.OK).json(application);
});

const createOne = catchAsync(async (req: Request, res: Response) => {
  const newApplication = await applicationService.createOne(req.body, req.user);
  const { programId } = req.body;

  const latestFile = await ProgramFiles.findOne({
    where: { programId },
    order: [['createdAt', 'DESC']],
  });
  console.log({ latestFile });
  req.body.summaryFile = latestFile ? latestFile.pdfUrl : '';
  if ((req.body.emails || []).length > 0) {
    await applicationService.sendNewAppEmails(req.body, req.user);
  }

  return res.status(httpStatus.CREATED).json(newApplication);
});

const updateOne = catchAsync(async (req: Request, res: Response) => {
  await applicationService.updateOne(req.body, req.user);
  return res.sendStatus(httpStatus.OK);
});

const deleteOne = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.body.data;
  await applicationService.deleteOne(id);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const exportCsv = catchAsync(async (req: Request, res: Response) => {
  if (req.user.clientCreatorId) {
    req.query.clientIds = [req.user.clientCreatorId];
  }

  const compressedFile = await applicationService.generateCsv(req.query, req.user);

  // Set proper headers for CSV file download (compressed as ZIP)
  res.setHeader('Content-Type', 'application/zip');
  res.setHeader('Content-Disposition', 'attachment; filename=applications.zip');

  return res.send(compressedFile);
});

const exportXlxs = catchAsync(async (req: Request, res: Response) => {
  if (req.user.clientCreatorId) {
    req.query.clientIds = [req.user.clientCreatorId];
  }

  const file = await applicationService.generateXlxs(req.query, req.user);

  // Set proper headers for XLSX file download
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=applications.xlsx');

  return res.send(file);
});

const updateMany = catchAsync(async (req: Request, res: Response) => {
  await applicationService.updateMany(req.body, req.user);
  return res.sendStatus(httpStatus.OK);
});

const createMany = catchAsync(async (req: Request, res: Response) => {
  const { emails = [] } = req.body;

  await applicationService.createMany(req.body);

  if ((emails || []).length > 0) await applicationService.sendMultiAppEmails(req.body, req.user);

  res.sendStatus(httpStatus.CREATED);
});

const deleteMany = catchAsync(async (req: Request, res: Response) => {
  const data: Record<string, any>[] = req.body?.data || req.body;
  const appIds = [...new Set(data?.map((app) => app.id))];

  await applicationService.deleteMany(appIds);

  return res.sendStatus(httpStatus.CREATED);
});

const createFile = catchAsync(async (req: Request, res: Response) => {
  const { applicationId, size, fileUrl, name, type } = req.body;
  const { files } = req;

  if (!files || Object.keys(files).length === 0) {
    return res.status(httpStatus.BAD_REQUEST).json({ message: 'No files provided for uploading.' });
  }

  const fileFields = {
    applicationId,
    name,
    size,
    type,
    fileUrl,
    file: files.file,
  };

  const result = await applicationService.createFile(fileFields);
  return res.status(httpStatus.CREATED).json(result);
});

const deleteFile = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  await applicationService.deleteFile(id);
  return res.sendStatus(httpStatus.NO_CONTENT);
});

const updateFileApplicationId = catchAsync(async (req: Request, res: Response) => {
  const { tempApplicationId, applicationId } = req.body;

  const updatedRows = await applicationService.updateFileApplicationId(
    tempApplicationId,
    applicationId
  );
  return res.status(httpStatus.OK).json({ updatedRows });
});

const getFilesByApplicationId = catchAsync(async (req: Request, res: Response) => {
  const { applicationId } = req.params;

  if (!applicationId) {
    return res.status(httpStatus.BAD_REQUEST).json({ message: 'applicationId is required.' });
  }

  const files = await applicationService.getFilesByApplicationId(applicationId);
  return res.status(httpStatus.OK).json(files);
});

module.exports = {
  list,
  search,
  getById,
  createOne,
  createMany,
  updateOne,
  updateMany,
  deleteOne,
  deleteMany,
  exportCsv,
  exportXlxs,
  createFile,
  deleteFile,
  updateFileApplicationId,
  getFilesByApplicationId,
};
